[{"id": 1, "row": 1, "ceil": 1, "name": "train_tech_name_1", "desc": "train_tech_desc_1", "icon": "kjd_icon_1"}, {"id": 10, "row": 2, "ceil": 1, "pre": [1], "name": "train_tech_name_10", "desc": "train_tech_desc_10", "icon": "kjd_icon_10"}, {"id": 8, "row": 2, "ceil": 2, "pre": [1], "name": "train_tech_name_8", "desc": "train_tech_desc_8", "icon": "kjd_icon_8"}, {"id": 13, "row": 2, "ceil": 3, "pre": [1], "name": "train_tech_name_13", "desc": "train_tech_desc_13", "icon": "kjd_icon_13"}, {"id": 18, "row": 3, "ceil": 1, "pre": [10], "name": "train_tech_name_18", "desc": "train_tech_desc_18", "icon": "kjd_icon_18"}, {"id": 11, "row": 3, "ceil": 2, "pre": [10, 8], "name": "train_tech_name_11", "desc": "train_tech_desc_11", "icon": "kjd_icon_11"}, {"id": 16, "row": 3, "ceil": 3, "pre": [8, 13], "name": "train_tech_name_16", "desc": "train_tech_desc_16", "icon": "kjd_icon_16"}, {"id": 5, "row": 3, "ceil": 4, "pre": [13], "name": "train_tech_name_5", "desc": "train_tech_desc_5", "icon": "kjd_icon_5"}, {"id": 9, "row": 4, "ceil": 1, "pre": [18], "name": "train_tech_name_9", "desc": "train_tech_desc_9", "icon": "kjd_icon_9"}, {"id": 19, "row": 4, "ceil": 2, "pre": [11], "name": "train_tech_name_19", "desc": "train_tech_desc_19", "icon": "kjd_icon_19"}, {"id": 7, "row": 4, "ceil": 3, "pre": [16], "name": "train_tech_name_7", "desc": "train_tech_desc_7", "icon": "kjd_icon_7"}, {"id": 14, "row": 4, "ceil": 4, "pre": [5], "name": "train_tech_name_14", "desc": "train_tech_desc_14", "icon": "kjd_icon_14"}, {"id": 3, "row": 5, "ceil": 1, "pre": [9, 11], "name": "train_tech_name_3", "desc": "train_tech_desc_3", "icon": "kjd_icon_3"}, {"id": 2, "row": 5, "ceil": 2, "pre": [7, 14], "name": "train_tech_name_2", "desc": "train_tech_desc_2", "icon": "kjd_icon_2"}, {"id": 25, "row": 6, "ceil": 1, "pre": [3], "name": "train_tech_name_25", "desc": "train_tech_desc_25", "icon": "kjd_icon_25"}, {"id": 23, "row": 6, "ceil": 2, "pre": [2], "name": "train_tech_name_23", "desc": "train_tech_desc_23", "icon": "kjd_icon_23"}, {"id": 17, "row": 7, "ceil": 1, "pre": [25], "name": "train_tech_name_17", "desc": "train_tech_desc_17", "icon": "kjd_icon_17"}, {"id": 21, "row": 7, "ceil": 2, "pre": [25, 23], "name": "train_tech_name_21", "desc": "train_tech_desc_21", "icon": "kjd_icon_21"}, {"id": 15, "row": 7, "ceil": 3, "pre": [23], "name": "train_tech_name_15", "desc": "train_tech_desc_15", "icon": "kjd_icon_15"}, {"id": 12, "row": 8, "ceil": 1, "pre": [17, 21], "name": "train_tech_name_12", "desc": "train_tech_desc_12", "icon": "kjd_icon_12"}, {"id": 27, "row": 8, "ceil": 2, "pre": [15, 21], "name": "train_tech_name_27", "desc": "train_tech_desc_27", "icon": "kjd_icon_27"}, {"id": 32, "row": 9, "ceil": 1, "pre": [12], "name": "train_tech_name_32", "desc": "train_tech_desc_32", "icon": "kjd_icon_32"}, {"id": 28, "row": 9, "ceil": 2, "pre": [12], "name": "train_tech_name_28", "desc": "train_tech_desc_28", "icon": "kjd_icon_28"}, {"id": 30, "row": 9, "ceil": 3, "pre": [27], "name": "train_tech_name_30", "desc": "train_tech_desc_30", "icon": "kjd_icon_30"}, {"id": 20, "row": 9, "ceil": 4, "pre": [27], "name": "train_tech_name_20", "desc": "train_tech_desc_20", "icon": "kjd_icon_20"}, {"id": 6, "row": 10, "ceil": 1, "pre": [32, 28, 30, 20], "name": "train_tech_name_6", "desc": "train_tech_desc_6", "icon": "kjd_icon_6"}, {"id": 43, "row": 11, "ceil": 1, "pre": [6], "name": "train_tech_name_43", "desc": "train_tech_desc_43", "icon": "kjd_icon_43"}, {"id": 33, "row": 11, "ceil": 2, "pre": [6], "name": "train_tech_name_33", "desc": "train_tech_desc_33", "icon": "kjd_icon_33"}, {"id": 41, "row": 11, "ceil": 3, "pre": [6], "name": "train_tech_name_41", "desc": "train_tech_desc_41", "icon": "kjd_icon_41"}, {"id": 24, "row": 12, "ceil": 1, "pre": [43, 33], "name": "train_tech_name_24", "desc": "train_tech_desc_24", "icon": "kjd_icon_24"}, {"id": 22, "row": 12, "ceil": 2, "pre": [33, 41], "name": "train_tech_name_22", "desc": "train_tech_desc_22", "icon": "kjd_icon_22"}, {"id": 31, "row": 13, "ceil": 1, "pre": [24], "name": "train_tech_name_31", "desc": "train_tech_desc_31", "icon": "kjd_icon_31"}, {"id": 29, "row": 13, "ceil": 2, "pre": [22], "name": "train_tech_name_29", "desc": "train_tech_desc_29", "icon": "kjd_icon_29"}, {"id": 44, "row": 14, "ceil": 1, "pre": [31, 29], "name": "train_tech_name_44", "desc": "train_tech_desc_44", "icon": "kjd_icon_44"}, {"id": 42, "row": 15, "ceil": 1, "pre": [44], "name": "train_tech_name_42", "desc": "train_tech_desc_42", "icon": "kjd_icon_42"}, {"id": 26, "row": 15, "ceil": 2, "pre": [44], "name": "train_tech_name_26", "desc": "train_tech_desc_26", "icon": "kjd_icon_26"}, {"id": 40, "row": 15, "ceil": 3, "pre": [44], "name": "train_tech_name_40", "desc": "train_tech_desc_40", "icon": "kjd_icon_40"}, {"id": 54, "row": 16, "ceil": 1, "pre": [42, 26], "name": "train_tech_name_54", "desc": "train_tech_desc_54", "icon": "kjd_icon_54"}, {"id": 52, "row": 16, "ceil": 2, "pre": [26, 40], "name": "train_tech_name_52", "desc": "train_tech_desc_52", "icon": "kjd_icon_52"}, {"id": 4, "row": 17, "ceil": 1, "pre": [6, 52], "name": "train_tech_name_4", "desc": "train_tech_desc_4", "icon": "kjd_icon_4"}, {"id": 57, "row": 18, "ceil": 1, "pre": [4], "name": "train_tech_name_57", "desc": "train_tech_desc_57", "icon": "kjd_icon_57"}, {"id": 55, "row": 18, "ceil": 2, "pre": [4], "name": "train_tech_name_55", "desc": "train_tech_desc_55", "icon": "kjd_icon_55"}, {"id": 53, "row": 19, "ceil": 1, "pre": [57], "name": "train_tech_name_53", "desc": "train_tech_desc_53", "icon": "kjd_icon_53"}, {"id": 51, "row": 19, "ceil": 2, "pre": [55], "name": "train_tech_name_51", "desc": "train_tech_desc_51", "icon": "kjd_icon_51"}, {"id": 58, "row": 20, "ceil": 1, "pre": [53, 51], "name": "train_tech_name_58", "desc": "train_tech_desc_58", "icon": "kjd_icon_58"}]