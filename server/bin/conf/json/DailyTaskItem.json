[{"id": 1, "weight": 1, "target": [{"type": 11, "id": 301}], "num": {"min": 1, "max": 4}, "checkFunction": "PLAY_COLLECT"}, {"id": 2, "weight": 1, "target": [{"type": 11, "id": 302}], "num": {"min": 1, "max": 4}, "checkFunction": "PLAY_COLLECT"}, {"id": 3, "weight": 1, "target": [{"type": 11, "id": 303}], "num": {"min": 1, "max": 4}, "checkFunction": "PLAY_COLLECT"}, {"id": 4, "weight": 1, "target": [{"type": 11, "id": 304}], "num": {"min": 1, "max": 4}, "checkFunction": "PLAY_COLLECT"}, {"id": 5, "weight": 1, "target": [{"type": 11, "id": 305}], "num": {"min": 1, "max": 4}, "checkFunction": "PLAY_COLLECT"}, {"id": 6, "weight": 1, "target": [{"type": 11, "id": 306}], "num": {"min": 1, "max": 4}, "checkFunction": "PLAY_COLLECT"}, {"id": 7, "weight": 1, "target": [{"type": 11, "id": 307}], "num": {"min": 1, "max": 4}, "checkFunction": "PLAY_COLLECT"}, {"id": 8, "weight": 1, "target": [{"type": 11, "id": 308}], "num": {"min": 1, "max": 4}, "checkFunction": "PLAY_COLLECT"}, {"id": 9, "weight": 1, "target": [{"type": 11, "id": 309}], "num": {"min": 1, "max": 4}, "checkFunction": "PLAY_COLLECT"}, {"id": 10, "weight": 1, "target": [{"type": 11, "id": 310}], "num": {"min": 1, "max": 4}, "checkFunction": "PLAY_COLLECT"}, {"id": 11, "weight": 1, "target": [{"type": 11, "id": 311}], "num": {"min": 1, "max": 4}, "checkFunction": "PLAY_COLLECT"}, {"id": 12, "weight": 1, "target": [{"type": 11, "id": 350}], "num": {"min": 1, "max": 4}, "checkFunction": "PLAY_COLLECT_2"}, {"id": 13, "weight": 1, "target": [{"type": 11, "id": 351}], "num": {"min": 1, "max": 4}, "checkFunction": "PLAY_COLLECT_2"}, {"id": 14, "weight": 1, "target": [{"type": 11, "id": 352}], "num": {"min": 1, "max": 4}, "checkFunction": "PLAY_COLLECT_2"}, {"id": 15, "weight": 1, "target": [{"type": 11, "id": 353}], "num": {"min": 1, "max": 4}, "checkFunction": "PLAY_COLLECT_2"}, {"id": 16, "weight": 1, "target": [{"type": 11, "id": 354}], "num": {"min": 1, "max": 4}, "checkFunction": "PLAY_COLLECT_2"}, {"id": 17, "weight": 1, "target": [{"type": 11, "id": 355}], "num": {"min": 1, "max": 4}, "checkFunction": "PLAY_COLLECT_2"}, {"id": 18, "weight": 1, "target": [{"type": 11, "id": 356}], "num": {"min": 1, "max": 4}, "checkFunction": "PLAY_COLLECT_2"}, {"id": 19, "weight": 1, "target": [{"type": 11, "id": 357}], "num": {"min": 1, "max": 4}, "checkFunction": "PLAY_COLLECT_2"}, {"id": 20, "weight": 1, "target": [{"type": 11, "id": 358}], "num": {"min": 1, "max": 4}, "checkFunction": "PLAY_COLLECT_2"}, {"id": 21, "weight": 1, "target": [{"type": 11, "id": 359}], "num": {"min": 1, "max": 4}, "checkFunction": "PLAY_COLLECT_2"}, {"id": 22, "weight": 1, "target": [{"type": 11, "id": 360}], "num": {"min": 1, "max": 4}, "checkFunction": "PLAY_COLLECT_2"}]