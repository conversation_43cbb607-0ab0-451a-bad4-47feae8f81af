package ut

import (
	"crypto/ecdsa"
	"crypto/md5"
	"crypto/x509"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"math/rand"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"time"
	"train/utils/array"

	"github.com/bwmarrin/snowflake"
	"github.com/dgrijalva/jwt-go"
	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
	"github.com/spf13/cast"
	"golang.org/x/crypto/ssh"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var (
	unique_id       = 0
	gen_id_last_now = 0
)

var snowflakeNode *snowflake.Node

func Init(sort int64) {
	snowflakeNode, _ = snowflake.NewNode(sort)
}

type DataWeight interface {
	GetWeight() int
}

type Number interface {
	int8 | int16 | int32 | int | int64 | uint8 | uint16 | uint32 | uint | uint64 | float32 | float64
}

const (
	TIME_MONTH  = 30 * 7 * 24 * 60 * 60 * 1000 //月
	TIME_WEEK   = 7 * 24 * 60 * 60 * 1000      //周
	TIME_DAY    = 24 * 60 * 60 * 1000          //天
	TIME_HOUR   = 60 * 60 * 1000               //时
	TIME_MINUTE = 60 * 1000                    //分
	TIME_SECOND = 1000                         //秒
)

// Now 当前的时间戳 毫秒
func Now() int {
	return int(time.Now().UnixNano() / 1e6)
}

// TimeUntilNextRefresh
/*
 * @description  获取lastRefresh之后的下一次刷新时间和now的差值
 * @param lastRefresh 上一次刷新的时间
 * @param now 当前时间
 * @param dailyRefreshTime 每日刷新时间
 * @param intervalDay 间隔?天
 * @return int
 */
func TimeUntilNextRefresh(lastRefresh, now, dailyRefreshTime, intervalDay int) int {
	nowTime := time.UnixMilli(int64(now))
	tmp := time.UnixMilli(int64(NowZeroTime() + dailyRefreshTime))
	lastRefreshTime := time.UnixMilli(int64(lastRefresh))
	// 获取每日刷新时间
	nextRefresh := time.Date(
		lastRefreshTime.Year(), lastRefreshTime.Month(), lastRefreshTime.Day(),
		tmp.Hour(), tmp.Minute(), 0, 0, lastRefreshTime.Location(),
	)
	// 如果 lastRefreshTime 已经过了今日刷新时间，则加一天
	if lastRefreshTime.After(nextRefresh) {
		nextRefresh = nextRefresh.Add(24 * time.Hour)
	}
	//if nextRefresh.Before(nowTime) {
	//	return int(nextRefresh.Sub(nowTime).Milliseconds())
	//}
	for intervalDay > 1 {
		nextRefresh = nextRefresh.Add(24 * time.Hour)
		intervalDay--
	}
	return int(nextRefresh.Sub(nowTime).Milliseconds())
}

// DateZeroTime 根据毫秒获取当天零点毫秒
func DateZeroTime(msec int) int {
	date := time.UnixMilli(int64(msec))
	year, month, day := date.Date()
	addTime := time.Date(year, month, day, 0, 0, 0, 0, date.Location())
	return int(addTime.UnixNano() / 1e6)
}

func NowZeroTime() int {
	date := time.Now()
	year, month, day := date.Date()
	addTime := time.Date(year, month, day, 0, 0, 0, 0, date.Location())
	return int(addTime.UnixNano() / 1e6)
}

// NextDateTime 根据毫秒获取下一天的零点毫秒
func NextDateTime(msec int) int {
	return DateZeroTime(msec) + TIME_DAY
}

func GetToDaySurplusTime(msec int, resetTime int) int {
	return GetToSurplusTime(msec, resetTime, 1)
}

// GetToDaySurplusTime 根据上次更新的毫秒，重置时间，周期，获取现在距离下次更新的剩余时间; 用于周期性刷新需求
func GetToSurplusTime(msec int, resetTime int, period int) int {
	nextUpdateTime := DateZeroTime(msec) + period*TIME_DAY + resetTime
	return nextUpdateTime - Now()
}

// 解析 "hh:mm" 转成时间戳
func StringToTime(timeStr string) int {
	datas := StringToInts(timeStr, ":")
	time := datas[0]*TIME_HOUR + datas[1]*TIME_MINUTE
	return time
}

// Random 生成随机数包括min和max
func Random(min int, max int) int {
	if min >= max {
		return min
	}
	return rand.Intn(int(math.Max(float64(max-min), 0))+1) + min
}

func RandomFloat64(min float64, max float64) float64 {
	if min >= max {
		return min
	}
	return min + (max-min)*rand.Float64()
}

// Floor 下取整
func Floor(val float64) int {
	return int(val)
}

// Ceil 上取整
func Ceil(val float64) int {
	return int(math.Ceil(val))
}

// Round 四舍五入
func Round(val float64) int {
	return int(math.Round(val))
}

// Chance 概率
func Chance(odds int) bool {
	mul := 100
	return odds > 0 && Random(0, 100*mul-1) < odds*mul
}

func ChanceLimitMax(odds int, max float64) bool {
	mul := 100.0
	return ChanceF(Min(float64(odds), (mul)*max))
}

func ChanceF(odds float64) bool {
	mul := 100.0
	return odds > 0 && RandomFloat64(0, 100*mul-1) < odds*mul
}

// RandomIndexByDataWeight 出错返回 -1
func RandomIndexByDataWeight(weights []DataWeight) int {
	return RandomIndexByWeight(weights, func(m DataWeight) int { return m.GetWeight() })
}

func RandomIndexByTotalWeight(weights []int) int {
	return RandomIndexByWeight(weights, func(m int) int { return m })
}

func RandomIndexByWeight[T any](weights []T, predicate func(T) int) int {
	cnt := len(weights)
	if cnt == 0 {
		return -1
	}
	totalWeight := 0
	for _, m := range weights {
		totalWeight += predicate(m)
	}
	if totalWeight > 0 {
		offset := Random(1, totalWeight)
		for i := 0; i < cnt; i++ {
			val := predicate(weights[i])
			offset -= val
			if offset <= 0 {
				return i
			}
		}
	}
	return Random(0, cnt-1)
}

func RandomIndexByWeightMap[K string | int, V any](data map[K]V, predicate func(K, V) int) (result K) {
	cnt := len(data)
	if cnt == 0 {
		return
	}
	totalWeight := 0
	for k, v := range data {
		totalWeight += predicate(k, v)
	}
	if totalWeight > 0 {
		offset := Random(1, totalWeight)
		for k, v := range data {
			val := predicate(k, v)
			offset -= val
			if offset <= 0 {
				return k
			}
		}
	}
	return
}

func RandomArray[T any](_array []T) []T {
	array := _array[:]
	for i := len(array) - 1; i >= 0; i-- {
		randomIndex := Random(0, 1e10+7) % (i + 1)
		a := array[randomIndex]
		b := array[i]
		array[i] = a
		array[randomIndex] = b
	}
	return array
}

// ID 唯一ID
func ID() string {
	now := Now()
	id := now*1000 + 1
	if now != gen_id_last_now {
		gen_id_last_now = now
		unique_id = 0
	} else if unique_id >= 999 {
		gen_id_last_now = now + 1
		unique_id = 0
		id = gen_id_last_now*1000 + 1
	} else {
		unique_id += 1
		id += unique_id
	}
	return strconv.Itoa(id)
}

// 生成一个随机7位UID
func UID7() string {
	return strconv.Itoa(Random(1000000, 9999999))
}
func UID8() string {
	return strconv.Itoa(Random(10000000, 99999999))
}
func UID6() string {
	return strconv.Itoa(Random(100000, 999999))
}

// 生成md5
func MD5(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

// If 三元判断
func If[T any](condition bool, trueVal, falseVal T) T {
	if condition {
		return trueVal
	}
	return falseVal
}

func Int(val interface{}) int {
	switch reply := val.(type) {
	case float32:
		return int(reply)
	case float64:
		return int(reply)
	case int32:
		return int(reply)
	case int64:
		return int(reply)
	case int:
		return reply
	case string:
		s, _ := strconv.Atoi(reply)
		return s
	case nil:
		return 0
	}
	return 0
}

func Float32(val interface{}) float32 {
	switch reply := val.(type) {
	case float32:
		return reply
	case float64:
		return float32(reply)
	case int:
		return float32(reply)
	case string:
		s, _ := strconv.ParseFloat(reply, 32)
		return float32(s)
	case nil:
		return 0
	}
	return 0
}

func Float64(val interface{}) float64 {
	switch reply := val.(type) {
	case float64:
		return reply
	case float32:
		return float64(reply)
	case int:
		return float64(reply)
	case string:
		s, _ := strconv.ParseFloat(reply, 64)
		return s
	case nil:
		return 0
	}
	return 0
}

func String(val interface{}) string {
	switch reply := val.(type) {
	case string:
		return reply
	case float64:
		return Itoa(reply)
	case int:
		return Itoa(reply)
	case bool:
		return strconv.FormatBool(reply)
	case nil:
		return ""
	}
	return ""
}

func Bool(val interface{}) bool {
	switch reply := val.(type) {
	case bool:
		return reply
	case nil:
		return false
	}
	return false
}

func IntArray(val interface{}) []int {
	switch reply := val.(type) {
	case []int:
		return reply
	case []float64:
		return array.Map(reply, func(m float64, _ int) int { return int(m) })
	case []interface{}:
		return array.Map(reply, func(m interface{}, _ int) int { return Int(m) })
	case nil:
		return []int{}
	}
	return []int{}
}

func StringArray(val interface{}) []string {
	switch reply := val.(type) {
	case []string:
		return reply
	case []interface{}:
		return array.Map(reply, func(m interface{}, _ int) string { return String(m) })
	case nil:
		return []string{}
	}
	return []string{}
}

func Bytes(v any) []byte {
	body, _ := json.Marshal(v)
	return body
}

func MapArray(data interface{}) []map[string]interface{} {
	arr := []map[string]interface{}{}
	if data == nil {
		return arr
	}
	switch reply := data.(type) {
	case []interface{}:
		for _, v := range reply {
			arr = append(arr, v.(map[string]interface{}))
		}
		return arr
	case []map[string]interface{}:
		return reply
	case primitive.A:
		for _, v := range reply {
			arr = append(arr, MapInterface(v))
		}
		return arr
	case nil:
	}
	return arr
}

func MapInterface(data interface{}) map[string]interface{} {
	switch reply := data.(type) {
	case map[string]interface{}:
		return reply
	case nil:
	}
	return map[string]interface{}{}
}

func RpcInterfaceMap(reply interface{}, _err interface{}) (ret map[string]interface{}, err string) {
	switch e := _err.(type) {
	case string:
		err = e
	case error:
		if _err != nil {
			err = e.Error()
		}
	}
	switch reply := reply.(type) {
	case map[string]interface{}:
		ret = reply
	}
	return
}

func RpcInt(reply interface{}, _err interface{}) (ret int, err string) {
	switch e := _err.(type) {
	case string:
		err = e
	case error:
		if _err != nil {
			err = e.Error()
		}
	}
	ret = Int(reply)
	return
}

func RpcBool(reply interface{}, _err interface{}) (ret bool, err string) {
	switch e := _err.(type) {
	case string:
		err = e
	case error:
		if _err != nil {
			err = e.Error()
		}
	}
	ret = Bool(reply)
	return
}

func Rpcbytes(reply interface{}, _err interface{}) (ret []byte, err string) {
	switch e := _err.(type) {
	case string:
		err = e
	case error:
		if _err != nil {
			err = e.Error()
		}
	}
	switch reply := reply.(type) {
	case []byte:
		ret = reply
	}
	return
}

// int转字符串
func Itoa(val interface{}) string {
	return strconv.Itoa(Int(val))
}

// string转int
func Atoi(val interface{}) int {
	r, _ := strconv.Atoi(String(val))
	return r
}

// string转Float
func Atof(val interface{}) float64 {
	r, _ := strconv.ParseFloat(String(val), 64)
	return r
}

// 将一个字符串拆分为数组
func StringToInts(val string, separator string) []int {
	if val == "" {
		return []int{}
	}
	arr := strings.Split(val, separator)
	ret := []int{}
	for _, s := range arr {
		ret = append(ret, Atoi(s))
	}
	return ret
}

func StringToFloats(val string, separator string) []float64 {
	if val == "" {
		return []float64{}
	}
	arr := strings.Split(val, separator)
	ret := []float64{}
	for _, s := range arr {
		ret = append(ret, Atof(s))
	}
	return ret
}

// WrapString 包装字符串
func WrapString(separator string, arr ...interface{}) string {
	if len(arr) == 0 {
		return ""
	}
	str := ""
	for _, m := range arr {
		if str != "" {
			str += separator
		}
		str += String(m)
	}
	return str
}

// GetStringLen 获取字符串长度 一个汉字2个长度
func GetStringLen(str string) int {
	if str == "" {
		return 0
	}
	count := 0
	for _, ch := range str {
		count += If((ch >= 0x0001 && ch <= 0x007e) || (0xff60 <= ch && ch <= 0xff9f), 1, 2)
	}
	return count
}

// WorkDir 获取当前的项目路径
func WorkDir() (dir string) {
	var err error
	dir, err = os.Getwd()
	if err != nil {
		file, _ := exec.LookPath(os.Args[0])
		ApplicationPath, _ := filepath.Abs(file)
		dir, _ = filepath.Split(ApplicationPath)
	}
	return
}

func SetTimeout(duration time.Duration, cb func()) {
	go func() {
		timer := time.NewTimer(duration)
		defer timer.Stop()
		select {
		case <-timer.C:
			cb()
		}
	}()
}

// LoadConfig 加载配置文件
func LoadConfig(filename string) ([]byte, error) {
	return LoadJsonByPath(filename, "/bin/conf/json/")
}

func LoadJsonByPath(filename string, path string) ([]byte, error) {
	file, err := os.Open(WorkDir() + path + filename + ".json")
	if err != nil {
		return nil, err
	}
	return io.ReadAll(file)
}

// Max 同类型数字max
func Max[T Number](a, b T) T {
	if a > b {
		return a
	}
	return b
}

// Min 同类型数字min
func Min[T Number](a, b T) T {
	if a > b {
		return b
	}
	return a
}

// Sum 同类型数字相加
func Sum[T Number](a, b T) T {
	return a + b
}

func Clamp01[T Number](a T) int {
	if a <= 0 {
		return 0
	}
	return 1
}

// ToInt32 数组转int32组
func ToInt32[T any](arr []T) []int32 {
	out := make([]int32, len(arr))
	for i, val := range arr {
		out[i] = cast.ToInt32(val)
	}
	return out
}

// ToUInt64 数组转int64组
func ToUInt64[T any](arr []T) []uint64 {
	out := make([]uint64, len(arr))
	for i, val := range arr {
		out[i] = cast.ToUint64(val)
	}
	return out
}

// ToInt 数组转int组
func ToInt[T any](arr []T) []int {
	out := make([]int, len(arr))
	for i, val := range arr {
		out[i] = cast.ToInt(val)
	}
	return out
}

func Abs(x int) int {
	return int(math.Abs(float64(x)))
}

// IsEmpty 字符串是否为空 忽略空格
func IsEmpty(str string) bool {
	return len(strings.ReplaceAll(str, " ", "")) == 0
}

func Unlock(lock *deadlock.Mutex) {
	if lock != nil {
		lock.Unlock()
	}
}

func TraceMemStats() {
	var ms runtime.MemStats
	runtime.ReadMemStats(&ms)
	log.Info("Alloc:%d, Sys:%d, HeapSys:%d, HeapAlloc:%d, HeapInuse:%d, HeapReleased:%d, StackSys:%d", ms.Alloc, ms.Sys, ms.HeapSys, ms.HeapAlloc, ms.HeapInuse, ms.HeapReleased, ms.StackSys)
}

// HexToInt64 hex 转 int64 忽略错误
func HexToInt64(hex string) int64 {
	i, _ := strconv.ParseInt(hex, 16, 32)
	return i
}

// GetApplicationDir 获取项目server根路径
func GetApplicationDir() string {
	dir, _ := os.Getwd()
	return dir
}

// ReadFileContent 从文件中读取内容 路径以server根目录为基准，例如/bin/twomiles.cn.key
func ReadFileContent(path string) (v string, err error) {
	if !strings.HasPrefix(path, "/") {
		path = fmt.Sprintf("/%s", path)
	}
	file, err := os.ReadFile(fmt.Sprintf("%s%s", GetApplicationDir(), path))
	if err != nil {
		return
	}
	v = string(file)
	return
}

// GetAge 根据身份证获取年龄
func GetAge(idCard string) int {
	if len(idCard) < 18 {
		return 0
	}
	birthday := idCard[6:14]
	t, err := time.Parse("20060102", birthday)
	if err != nil {
		return 0
	}
	age := time.Now().Sub(t).Hours() / 24 / 365
	return cast.ToInt(age)
}

// Trim 字符串去掉所有空格
func Trim(str string) string {
	return strings.Join(strings.Fields(str), "")
}

// InitialIsChar 判断首个字符是不是字母 upper=true时区分大小写
func InitialIsChar(line string, upper bool) bool {
	line = Trim(line)
	if len(line) == 0 {
		return false
	}
	reg := "^[A-Za-z]"
	if upper {
		reg = "^[A-Z]"
	}
	matched, _ := regexp.Match(reg, []byte(line))
	return matched
}

// NotHasSuffixOrAppend  判断字符串str是不是以suffix结尾，如果不是，则追加dist字符串到末尾并返回str
func NotHasSuffixOrAppend(str string, suffix, dist string) string {
	if !strings.HasSuffix(str, suffix) {
		return fmt.Sprintf("%s%s", str, dist)
	}
	return str
}

// HasSuffixOrRemove 判断字符串str是不是以suffix结尾，如果是，则移除末尾的suffix并返回str
func HasSuffixOrRemove(str string, suffix string) string {
	if strings.HasSuffix(str, suffix) {
		return str[:len(str)-len(suffix)]
	}
	return str
}

// NotHasPrefixOrAppend 判断字符串str是不是以prefix开头，如果不是，则追加dist字符串到开头并返回str
func NotHasPrefixOrAppend(str string, prefix, dist string) string {
	if !strings.HasPrefix(str, prefix) {
		return fmt.Sprintf("%s%s", dist, str)
	}
	return str
}

// HasPrefixOrRemove 判断字符串str是不是以prefix开头，如果是，则移除开头的prefix并返回str
func HasPrefixOrRemove(str string, prefix string) string {
	if strings.HasPrefix(str, prefix) {
		return strings.Replace(str, prefix, "", 1)
	}
	return str
}

// ParseWorldTime 转换player对象的Time字段为游戏时间
func ParseWorldTime(time uint64) (day, hour, minute, second, ms uint64) {
	// 自然时间1秒 = 游戏时间 120秒
	// 将时间差转换成秒
	timeDiff := time * 120

	day = timeDiff / TIME_DAY
	remainingSeconds := timeDiff % TIME_DAY
	hour = remainingSeconds / TIME_HOUR
	remainingSeconds = remainingSeconds % TIME_HOUR
	minute = remainingSeconds / TIME_MINUTE
	remainingSeconds = remainingSeconds % TIME_MINUTE
	second = remainingSeconds / TIME_SECOND
	remainingSeconds = remainingSeconds % TIME_SECOND
	ms = remainingSeconds
	return
}

// GetExecuteTimeWrapFunc wrap一个方法 输出执行时间
func GetExecuteTimeWrapFunc(f func()) func() {
	return func() {
		begin := time.Now()
		defer func() {
			log.Debug("execute during: %fs", time.Since(begin).Seconds())
		}()
		f()
	}
}

func ToFixed2(val float64) float64 {
	return ToFixed(val, 2)
}

func ToFixed(val float64, precision int) float64 {
	return math.Floor(val*math.Pow10(precision)) / math.Pow10(precision)
}

func ToRound2(val float64) float64 {
	return ToRound(val, 2)
}

func ToRound(val float64, precision int) float64 {
	return math.Round(val*math.Pow10(precision)) / math.Pow10(precision)
}

// FirstToLower 首字母转小写
func FirstToLower(str string) string {
	firstChar := str[:1]
	return strings.ToLower(firstChar) + str[1:]
}

func roundToDecimal(n float64, decimals int) float64 {
	shift := math.Pow(10, float64(decimals))
	return math.Round(n*shift) / shift
}

func RoundNumber[T Number](n T) T {
	sn := cast.ToString(n)

	if len(sn) <= 2 {
		return T(5 * roundToDecimal(float64(n/5), 0))
	}

	return T(roundToDecimal(float64(n), 2-len(sn)))
}

// 适用于比较1.0.1这类版本号,支持长度不限
// 返回值>0表示A比B大，0表示相等
func CmpVersion(a, b string) int {
	vA := StringToInts(a, ".")
	vB := StringToInts(b, ".")
	vALen := len(vA)
	vBLen := len(vB)
	for i := 0; i < vALen; i++ {
		a, b := vA[i], 0
		if i < vBLen {
			b = vB[i]
		}
		if a == b {
			continue
		} else {
			return a - b
		}
	}
	if vBLen > vALen {
		return -1
	} else {
		return 0
	}
}

// ------------------------------ ssh相关 --------------------------------
// 远程执行脚本
func SshExcuteShell(remoteAddr, cmd string, args ...string) (rst string, err error) {
	log.Info("sshExcuteShell start remoteAddr: %v, cmd: %v, args: %v", remoteAddr, cmd, args)
	pemPath := "./jiuwanmu.pem"
	key, err := loadPEM(pemPath)
	if err != nil {
		log.Error("Failed to load PEM file: %v", err)
	}

	// SSH 连接配置
	config := &ssh.ClientConfig{
		User: "root",
		Auth: []ssh.AuthMethod{
			ssh.PublicKeys(key),
		},
		// Auth: []ssh.AuthMethod{
		// 	ssh.Password("password"),
		// },
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
	}

	// 建立 SSH 连接
	conn, err := ssh.Dial("tcp", fmt.Sprintf("%s:22", remoteAddr), config)
	if err != nil {
		log.Error("Failed to dial: %v", err)
	}
	defer conn.Close()

	// 创建 SSH 会话
	session, err := conn.NewSession()
	if err != nil {
		log.Error("Failed to create session: %v", err)
	}
	defer session.Close()

	// 执行远程命令
	output, err := session.CombinedOutput(cmd + " " + escapeArgs(args))
	if err != nil {
		log.Error("Failed to execute command: %v", err)
	}

	// 输出命令执行结果
	rst = string(output)
	log.Info("sshExcuteShell rst: %v", rst)
	return
}

// 加载 PEM 文件
func loadPEM(pemPath string) (ssh.Signer, error) {
	pemBytes, err := os.ReadFile(pemPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read PEM file: %v", err)
	}

	signer, err := ssh.ParsePrivateKey(pemBytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %v", err)
	}

	return signer, nil
}

// 转义参数
func escapeArgs(args []string) string {
	escapedArgs := make([]string, len(args))
	for i, arg := range args {
		escapedArgs[i] = fmt.Sprintf("%q", arg)
	}
	return strings.Join(escapedArgs, " ")
}

// 根据x509证书字符串解析证书
func GetCertByCertStr(certStr string) (cert *x509.Certificate, err error) {
	// 解码Base64编码的证书字符串
	derBytes, err := base64.StdEncoding.DecodeString(certStr)
	if err != nil {
		log.Error("getpublicKeyByCertStr base64 certStr: %v err: %v", certStr, err)
		return
	}

	// 解析X.509证书
	cert, err = x509.ParseCertificate(derBytes)
	if err != nil {
		log.Error("getpublicKeyByCertStr cert parse certStr: %v err: %v", certStr, err)
		return
	}
	return
}

// 验证证书信任链 [终端证书 中间证书 根证书]
func VerifyCertChain(certChain []*x509.Certificate) error {
	if len(certChain) < 3 {
		return errors.New("certChain len error")
	}
	// 创建一个根证书池，用于验证证书链中的证书
	roots := x509.NewCertPool()
	roots.AddCert(certChain[2]) // 将根证书添加到根证书池

	// 验证证书链
	opts := x509.VerifyOptions{
		Roots:         x509.NewCertPool(),                      // 根证书池
		CurrentTime:   time.Now(),                              // 指定当前时间
		KeyUsages:     []x509.ExtKeyUsage{x509.ExtKeyUsageAny}, // 指定密钥用途（可根据需要调整）
		Intermediates: x509.NewCertPool(),                      // 中间证书池
	}
	opts.Roots.AddCert(certChain[2])         // 将根证书添加到根证书池
	opts.Intermediates.AddCert(certChain[1]) // 将中间证书添加到中间证书池

	// 验证证书
	_, err := certChain[0].Verify(opts)
	return err
}

// 解析JWS数据
func ParseJwsToken(signedPayload string, publicKey *ecdsa.PublicKey) (jwt.MapClaims, error) {
	// 解析JWS格式的推送信息
	retToken, err := jwt.Parse(signedPayload, func(token *jwt.Token) (interface{}, error) {
		return publicKey, nil
	})
	if err != nil {
		log.Error("ParseJwsToken reponse jwt err: %v", err)
		return nil, err
	}
	mapClaims := retToken.Claims.(jwt.MapClaims)
	if mapClaims == nil {
		errStr := "ParseJwsToken mapClaims nil"
		log.Error(errStr)
		err = errors.New(errStr)
		return nil, err
	}
	return mapClaims, nil
}

// Spread
/*
 * @description 广度优先搜索
 * @param grid
 * @param x 起点x
 * @param y 起点y
 * @param call 回调
 */
func Spread(grid [][]int, x, y int, call func(x, y int)) {
	// 定义四个方向
	dx := []int{-1, 1, 0, 0}
	dy := []int{0, 0, -1, 1}
	// 创建队列，并将起始点放入队列
	queue := [][]int{{x, y}}
	// 循环直到队列为空
	for len(queue) > 0 {
		// 取出队列首部的点
		point := queue[0]
		// 队列去掉首部的点
		queue = queue[1:]
		// 遍历这个点的四个方向
		for i := 0; i < 4; i++ {
			nx, ny := point[0]+dx[i], point[1]+dy[i]
			// 检查新点是否在网格内，以及这个点的值是否为0
			if nx >= 0 && nx < len(grid) && ny >= 0 && ny < len(grid[0]) && grid[nx][ny] == 0 {
				if call != nil {
					call(nx, ny)
				}
				// 重新顺便复制，不然死循环
				grid[nx][ny] = 2
				queue = append(queue, []int{nx, ny})
			}
		}
	}
}

func NumAvgSplit(number int, count int) []int {
	arr := make([]int, count)
	base := number / count
	remain := number - base*count
	for i := 0; i < count; i++ {
		add := 0
		if remain > 0 {
			remain--
			add++
		}
		arr[i] = base + add
	}
	return arr
}

// GetNextWeekRefreshTime
/*
 * @description 获取下一次 周刷新时间
 * @param hourOfDay 每日刷新时间
 * @return int
 */
func GetNextWeekRefreshTime(hourOfDay, lastRefresh int) int {
	hourOfDay = (hourOfDay + 24) % 24
	date := time.UnixMilli(int64(lastRefresh))
	year, month, day := date.Date()
	addTime := time.Date(year, month, day, hourOfDay, 0, 0, 0, date.Location())
	weekday := addTime.Weekday()
	diff := int(7-weekday) + 1
	if diff > 7 {
		diff = diff - 7
	}
	nextMonday := addTime.AddDate(0, 0, diff)
	// 转换为时间戳
	return int(nextMonday.UnixNano() / 1e6)
}

// GetNextRefreshTimeOfDoubleWeek 获取下一次双周刷新时间
// refreshOffset: 每天刷新的时间偏移量（毫秒）
// 返回下次刷新的具体时间戳（毫秒）
func GetNextRefreshTimeOfDoubleWeek(refreshOffset int64) int64 {
	now := time.Now()
	// 获取当前时间所在周的周一凌晨时间
	thisMonday := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	for thisMonday.Weekday() != time.Monday {
		thisMonday = thisMonday.AddDate(0, 0, -1)
	}
	// 从1970第一个周一开始算起，计算到现在过了多少个双周
	firstMonday := time.Date(1970, 1, 5, 0, 0, 0, 0, now.Location()) // 1970-01-05是第一个周一
	weeksSince := int64(thisMonday.Sub(firstMonday).Hours() / (24 * 7))
	periodNumber := weeksSince / 2
	// 计算当前双周期的开始时间（周一凌晨）
	currentPeriodStart := firstMonday.Add(time.Duration(periodNumber*2*7*24) * time.Hour)
	// 如果当前时间已经过了本周期的刷新时间，移到下一个周期
	refreshTime := currentPeriodStart.UnixMilli() + refreshOffset
	if now.UnixMilli() >= refreshTime {
		refreshTime += 14 * 24 * 60 * 60 * 1000 // 加14天
	}
	return refreshTime
}

func GenId() int64 {
	return snowflakeNode.Generate().Int64()
}

func TimeScore() float64 {
	uniqueId := snowflakeNode.Generate().Int64()
	return (1 - float64(Now()/1000)/1e10) + float64(uniqueId%10000)/100000000.0
}

func Today() int { return Now() / TIME_DAY }

// BsonToReadableJson 将BSON数据转换为可读的JSON字符串
func BsonToReadableJson(bsonData []byte) (string, error) {
	// 先解码到map
	var data map[string]interface{}
	if err := bson.Unmarshal(bsonData, &data); err != nil {
		return "", err
	}

	// 再编码为JSON，这样会自动处理大部分MongoDB类型
	jsonBytes, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return "", err
	}

	return string(jsonBytes), nil
}

// JsonToBson 将JSON数据转换为BSON格式
func JsonToBson(jsonData []byte) ([]byte, error) {
	var tempObj interface{}
	if err := json.Unmarshal(jsonData, &tempObj); err != nil {
		return nil, err
	}

	bsonBytes, err := bson.Marshal(tempObj)
	if err != nil {
		return nil, err
	}

	return bsonBytes, nil
}

// JsonToBsonUnmarshal 将JSON数据转换为BSON并解析到目标结构体
func JsonToBsonUnmarshal(jsonData []byte, v any) error {
	var tempObj interface{}
	if err := json.Unmarshal(jsonData, &tempObj); err != nil {
		return err
	}

	bsonBytes, err := bson.Marshal(tempObj)
	if err != nil {
		return err
	}

	return bson.Unmarshal(bsonBytes, v)
}

func RandomIn[T any](ary []T) T {
	if len(ary) == 0 {
		return *new(T)
	}
	return ary[Random(0, len(ary)-1)]
}

// 数组越界
func IsIndexOutOfBounds[T any](ary []T, index int) bool {
	if ary == nil || len(ary) == 0 {
		return true
	}
	return index < 0 || index >= len(ary)
}

// func Testxxx() {
// nextRefreshTime := GetNextRefreshTimeOfDoubleWeek(18000000)
// now := time.Now().UnixMilli()
// fmt.Printf("当前时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
// fmt.Printf("下次刷新时间: %s\n", time.UnixMilli(nextRefreshTime).Format("2006-01-02 15:04:05"))
// diffDays := float64(nextRefreshTime-now) / float64(24*60*60*1000)
// fmt.Printf("距离下次刷新还有%.2f天\n", diffDays)
// }

// Clamp 将值限制在min和max之间
func Clamp[T Number](value, min, max T) T {
	if value < min {
		return min
	}
	if value > max {
		return max
	}
	return value
}

// Rect 表示一个中心点+宽高的矩形
// Cx, Cy: 中心点坐标，W, H: 宽高
type Rect struct {
	Cx float64
	Cy float64
	W  float64
	H  float64
}

// RectIntersectArea 计算两个矩形的相交面积
func RectIntersectArea(a, b *Rect) float64 {
	leftA := a.Cx - a.W*0.5
	rightA := a.Cx + a.W*0.5
	bottomA := a.Cy - a.H*0.5
	topA := a.Cy + a.H*0.5

	leftB := b.Cx - b.W*0.5
	rightB := b.Cx + b.W*0.5
	bottomB := b.Cy - b.H*0.5
	topB := b.Cy + b.H*0.5

	intersectW := Max(0.0, Min(rightA, rightB)-Max(leftA, leftB))
	intersectH := Max(0.0, Min(topA, topB)-Max(bottomA, bottomB))
	return intersectW * intersectH
}
