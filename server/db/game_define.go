package db

// 用户数据表
var USER = Def("user").Schema(map[string]bool{"username": true})

// 区服数据表
var AREA = Def("area").Schema(map[string]bool{"id": true})

// 角色数据表
var PLAYER = Def("player_1").Schema(map[string]bool{"id": true, "nickName": false})

// 计数表
var COUNTER = Def("counter").Schema(map[string]bool{"id": true, "seq": false})

// 玩家数据记录表
var RECORD = Def("record_1").Schema(map[string]bool{"id": true})

// 玩家邮件
var MAIL = Def("mail_1").
	Schema(map[string]bool{
		"uid":  false, // 玩家id 唯一索引
		"id":   false,
		"time": false,
	}).
	Index(IndexDef{
		Fields: []IndexField{
			{Field: "uid", Sort: 1},
			{Field: "id", Sort: 1},
		},
	})

// 兑换码数据
var CDK = Def("cdk").Schema(map[string]bool{"code": true})

// 兑换码记录
var CDK_RECORD = Def("cdk_record").Schema(map[string]bool{"uid": true})

// 时间宝石存档记录
var TIME_STONE_RECORD = Def("time_stone_record").Schema(map[string]bool{"uid": true})

// 可回档记录
var FIX_RECORD = Def("record").Schema(map[string]bool{
	"id":        false, // 玩家id
	"timestamp": false, // 创建时间
})

// 普通竞技场战斗记录
var PVP_RECORD_NORMAL = Def("pvp_record_normal").
	Schema(map[string]bool{
		"attacker":  false, // 进攻方
		"defender":  false, // 防守方
		"timestamp": false,
	}).
	Index(IndexDef{
		// 进攻方相关查询的索引 时间降序
		Fields: []IndexField{
			{Field: "attacker", Sort: 1},
			{Field: "timestamp", Sort: -1},
			{Field: "result", Sort: 1},
		},
	}).
	Index(IndexDef{
		// 防守方相关查询的索引
		Fields: []IndexField{
			{Field: "defender", Sort: 1},
			{Field: "timestamp", Sort: -1},
			{Field: "result", Sort: 1},
		},
	}).
	Index(IndexDef{
		// 防守方相关查询的索引
		Fields: []IndexField{
			{Field: "attacker", Sort: 1},
			{Field: "defender", Sort: 1},
			{Field: "result", Sort: 1},
		},
	})

// 支付订单
var PAY = Def("pay").
	Schema(map[string]bool{
		"orderId":      true, // 订单id
		"thirdOrderId": false,
		"userId":       false,
		"state":        false,
		"productId":    false,
	}).
	Index(IndexDef{
		Fields: []IndexField{
			{Field: "orderId", Sort: 1},
			{Field: "userId", Sort: 1},
			{Field: "state", Sort: 1},
		},
	}).
	Index(IndexDef{
		Fields: []IndexField{
			{Field: "thirdOrderId", Sort: 1},
			{Field: "userId", Sort: 1},
			{Field: "state", Sort: 1},
		},
	}).
	Index(IndexDef{
		Fields: []IndexField{
			{Field: "userId", Sort: 1},
			{Field: "productId", Sort: 1},
			{Field: "state", Sort: 1},
		},
	})
