package game

import (
	"math"
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/data/item_id"
	"train/base/enum/character_quality"
	"train/base/enum/item_type"
	"train/base/structs"
	"train/common/pb"
	"train/common/ta"
	ut "train/utils"
	"train/utils/array"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/types/known/anypb"
)

// InitBagHD 自动生成，不要在这个方法添加任何内容。
func InitBagHD(this *Game) {
	// 车票碎片合成
	this.middleware.Wrap("C2S_TicketMergeMessage", this.C2sTicketMergeMessageHandler)
	// 丢弃物品
	this.middleware.Wrap("C2S_DropItemMessage", this.C2sDropItemMessageHandler)
	// 空间宝石升级
	this.middleware.Wrap("C2S_SpaceStoneLvUpMessage", this.C2sSpaceStoneLvUpMessageHandler)
	// 使用空间宝石传送
	this.middleware.Wrap("C2S_UseSpaceStoneMessage", this.C2sUseSpaceStoneMessageHandler)
	// 使用空间宝石标记
	this.middleware.Wrap("C2S_MarkSpaceStoneMessage", this.C2sMarkSpaceStoneMessageHandler)
	// 同步物品
	this.middleware.Wrap("C2S_SyncItemMessage", this.C2sSyncItemMessageHandler)
	// 使用物品
	this.middleware.Wrap("C2S_UseItemMessage", this.C2sUseItemMessageHandler)
	// 时间宝石升级
	this.middleware.Wrap("C2S_TimeStoneLvUpMessage", this.C2sTimeStoneLvUpMessageHandler)
	// 时间宝石记录
	this.middleware.Wrap("C2S_TimeStoneRecordMessage", this.C2sTimeStoneRecordMessageHandler)
	// 使用时间宝石
	this.middleware.Wrap("C2S_TimeStoneUseMessage", this.C2sTimeStoneUseMessageHandler)
}
func (this *Game) C2sTicketMergeMessageHandler(player *structs.Player, msg *pb.C2S_TicketMergeMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) *pb.S2C_TicketMergeMessage {
		return &pb.S2C_TicketMergeMessage{Code: code}
	}
	exChangeNum := cast.ToInt(msg.GetNum())
	misc := cfg.GetMisc()
	ratio := misc.Ticket.MergeCnt
	frag := &structs.Condition{Id: item_id.TICKET_FRAG, Type: condition.PROP}
	curNum := player.GetNumByCondition(frag)
	needNum := exChangeNum * ratio
	frag.Num = needNum

	if needNum > curNum {
		return send(1)
	}

	player.DeductCost(frag, ta.ResChangeSceneTypeBag)
	player.GrantReward(&structs.Condition{Id: item_id.TICKET, Type: condition.PROP, Num: exChangeNum}, ta.ResChangeSceneTypeBag)
	return send(0)
	//@action-code-end
}
func (this *Game) C2sDropItemMessageHandler(player *structs.Player, msg *pb.C2S_DropItemMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) *pb.S2C_DropItemMessage {
		return &pb.S2C_DropItemMessage{Code: code}
	}
	item := msg.GetItem()
	if item.Type != condition.PROP {
		return send(1)
	}
	cond := &structs.Condition{}
	cond.FromPb(item)
	id := cast.ToInt(cond.Id)
	bean, _ := cfg.ItemContainer.GetBeanById(id)
	if bean == nil {
		return send(2)
	}
	if bean.Type != item_type.COLLECT {
		return send(2)
	}
	player.DeductCost(cond, ta.ResChangeSceneTypeBag)
	return send(0)
	//@action-code-end
}
func (this *Game) C2sSpaceStoneLvUpMessageHandler(player *structs.Player, msg *pb.C2S_SpaceStoneLvUpMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_SpaceStoneLvUpMessage{Code: cast.ToInt32(code)}
	}
	spaceStone := player.SpaceStone
	bean, _ := cfg.SpaceStoneContainer.GetBeanById(spaceStone.Lv + 1)
	if bean == nil {
		return send(1)
	}
	costs := structs.ToConditions(bean.BuyCost)
	if len(player.CheckConditions(costs)) > 0 {
		return send(2)
	}
	player.DeductCosts(costs, ta.ResChangeSceneTypeSpaceStone)
	spaceStone.LvUp()
	return &pb.S2C_SpaceStoneLvUpMessage{Code: 0}
	//@action-code-end
}
func (this *Game) C2sUseSpaceStoneMessageHandler(player *structs.Player, msg *pb.C2S_UseSpaceStoneMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_UseSpaceStoneMessage{Code: cast.ToInt32(code)}
	}
	id := cast.ToInt(msg.GetId())
	spaceStone := player.SpaceStone
	if !spaceStone.HasMark(id) {
		return send(1)
	}
	planet := player.PlanetData.GetPlanet(id)
	if planet == nil {
		return send(2)
	}

	misc := cfg.GetMisc()
	cost := misc.SpaceStone.Cost
	if spaceStone.Energy < cost {
		return send(3)
	}
	spaceStone.Energy -= cost
	player.PlanetData.ReachPlanet(id)
	return send(0)
	//@action-code-end
}
func (this *Game) C2sMarkSpaceStoneMessageHandler(player *structs.Player, msg *pb.C2S_MarkSpaceStoneMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_MarkSpaceStoneMessage{Code: cast.ToInt32(code)}
	}
	id := cast.ToInt(msg.GetId())
	removeId := cast.ToInt(msg.GetRemoveId())
	spaceStone := player.SpaceStone
	if spaceStone.HasMark(id) {
		return send(1)
	}
	if spaceStone.GetMaxMarkCnt() <= len(spaceStone.Marks) && removeId == 0 {
		return send(2)
	}
	planet := player.PlanetData.GetPlanet(id)
	if planet == nil {
		return send(3)
	}
	spaceStone.RemoveMark(removeId)
	spaceStone.Mark(id)
	return send(0)
	//@action-code-end
}
func (this *Game) C2sSyncItemMessageHandler(player *structs.Player, msg *pb.C2S_SyncItemMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	return nil
	//@action-code-end
}
func (this *Game) C2sUseItemMessageHandler(player *structs.Player, msg *pb.C2S_UseItemMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	result := make([]*structs.Condition, 0)
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_UseItemMessage{
			Code:    cast.ToInt32(code),
			Rewards: lo.Map(result, func(t *structs.Condition, i int) *pb.Condition { return t.ToPb() }),
		}
	}
	cond := &structs.Condition{}
	cond.FromPb(msg.GetItem())
	if cond.Num <= 0 {
		cond.Num = 1
	}
	if !player.CheckCondition(cond) {
		return send(1)
	}
	if cond.Type == condition.PROP {
		id := cast.ToInt(cond.Id)
		switch cond.Id {
		case item_id.InviteCard1:
			fallthrough
		case item_id.InviteCard2:
			fallthrough
		case item_id.InviteCard3:
			arr := lo.Filter(cfg.CharacterContainer.GetData(), func(character *cfg.Character[int], i int) bool {
				return character.Quality == character_quality.GetInviteCardQuality(id)
			})
			for i := 0; i < cond.Num; i++ {
				result = append(result, player.CheckChangePassenger(player.RandomPassenger(arr...), result))
			}
		}
		itemBean, _ := cfg.ItemContainer.GetBeanById(id)
		if itemBean == nil {
			return send(2)
		}
		switch itemBean.Type {
		case item_type.ROLE_TICKET:
			charaBean, _ := cfg.CharacterContainer.GetBeanById(id)
			if charaBean == nil {
				return send(3)
			}
			result = append(result, player.CheckChangePassenger(charaBean, result))
		case item_type.TIME_BOX:
			uid := cast.ToString(cond.Extra["uid"])
			if uid == "" {
				return send(4)
			}
			item := player.GetItemByUid(uid)
			if item == nil {
				return send(5)
			}
			if item.GetSurplusTime() > 0 {
				return send(6)
			}
			misc := cfg.GetMisc()
			timeBoxBean := array.Find(misc.TimeBox, func(t *cfg.TimeBoxConfig) bool {
				return t.Id == id
			})
			if timeBoxBean == nil {
				return send(7)
			}
			rewards := player.GenerateRewards(timeBoxBean.Rewards, nil)
			result = append(result, rewards...)
		case item_type.RANDOM_BOX:
			for i := 0; i < cond.Num; i++ {
				rewards := player.GenRandomBoxRewards(id)
				result = append(result, rewards...)
			}
		}
		player.DeductCost(cond, ta.ResChangeSceneTypeBag)
		player.GrantRewards(result, ta.ResChangeSceneTypeBag)
		return send(0)
	}
	return send(2)
	//@action-code-end
}
func (this *Game) C2sTimeStoneLvUpMessageHandler(player *structs.Player, msg *pb.C2S_TimeStoneLvUpMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_TimeStoneLvUpMessage{Code: cast.ToInt32(code)}
	}
	timeStone := player.TimeStone
	bean, _ := cfg.TimeStoneContainer.GetBeanById(timeStone.Lv + 1)
	if bean == nil {
		return send(1)
	}
	costs := structs.ToConditions(bean.BuyCost)
	if len(player.CheckConditions(costs)) > 0 {
		return send(2)
	}
	player.DeductCosts(costs, ta.ResChangeSceneTypeUnknown)
	timeStone.LvUp()
	return &pb.S2C_TimeStoneLvUpMessage{Code: 0}
	//@action-code-end
}
func (this *Game) C2sTimeStoneRecordMessageHandler(player *structs.Player, msg *pb.C2S_TimeStoneRecordMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	r := player.TimeStone.Events
	return &pb.S2C_TimeStoneRecordMessage{Records: lo.Map(r, func(t *structs.TimeStoneEventWrapper, i int) *pb.TimeStoneEventWrapper { return t.ToPb() })}
	//@action-code-end
}
func (this *Game) C2sTimeStoneUseMessageHandler(player *structs.Player, msg *pb.C2S_TimeStoneUseMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	indexAry := ut.ToInt(msg.GetIndex())
	if len(indexAry) == 0 {
		return &pb.S2C_TimeStoneUseMessage{Code: 1}
	}
	events := player.TimeStone.Events
	reqAry := make([]*structs.TimeStoneEventWrapper, 0)
	for _, index := range indexAry {
		if ut.IsIndexOutOfBounds(events, index) {
			return &pb.S2C_TimeStoneUseMessage{Code: 2}
		}
		evt := events[index]
		if evt.Type != msg.GetType() {
			return &pb.S2C_TimeStoneUseMessage{Code: 3}
		}
		if !evt.GetEvent().CanUse() {
			return &pb.S2C_TimeStoneUseMessage{Code: 4}
		}
		reqAry = append(reqAry, evt)
	}
	if len(reqAry) == 0 {
		return &pb.S2C_TimeStoneUseMessage{Code: 5}
	}

	misc := cfg.GetMisc()

	// 增加
	add := make([]*structs.Condition, 0)
	// 扣除
	deduct := make([]*structs.Condition, 0)
	// 这里是需要执行的扣除方法，不需要做检查的那种
	deductFn := make([]func(), 0)

	var respData *anypb.Any
	energyCost := 0
	for _, evt := range reqAry {
		costBean := cfg.TimeStoneEventContainer.GetBean(int(evt.GetEvent().GetType()))
		energyCost += costBean.Cost
		// 能量不足了
		if energyCost > player.TimeStone.Energy {
			return &pb.S2C_TimeStoneUseMessage{Code: 7}
		}
		switch evt.Type {
		case pb.TimeStoneEvent_TypeJackpot:
			data := evt.GetEvent().(*structs.JackpotEvent)
			data.Expired = true
			cond := &structs.Condition{}
			switch data.DrawType {
			case 1:
				cond.Type = condition.PROP
				cond.Id = item_id.TICKET
				cond.Num = 1
			case 2:
				cond.Type = condition.DIAMOND
				cond.Num = misc.JackPotPrice
				if data.IsDiamondDiscount {
					cond.Num = cast.ToInt(math.Floor(cast.ToFloat64(misc.JackPotPrice) * misc.JackPotDiscount))
				}
			}
			add = structs.MergeConditions([]*structs.Condition{cond}, add)
			// 检查扣除 如果是碎片，则扣除物品，否则扣除乘客 这里会先做检查
			if data.IsConvertIntoFragments {
				cond := &structs.Condition{Type: condition.PASSENGER_FRAG, Id: data.DrawPid, Num: -1}
				bol := player.CheckCondition(cond)
				if !bol {
					// 碎片扣除检查不通过
					return &pb.S2C_TimeStoneUseMessage{Code: 6}
				}
				deduct = append(deduct, cond)
			} else {
				deductFn = append(deductFn, func() {
					player.Passenger = lo.Filter(player.Passenger, func(p *structs.Passenger, i int) bool { return p.Id != data.DrawPid })
				})
			}
		case pb.TimeStoneEvent_TypeBlackHoleBattle:
			data := evt.GetEvent().(*structs.BlackHoleBattleEvent)
			data.Expired = true
			r := data.Node.Rewards
			if len(player.CheckConditions(r)) > 0 {
				// 扣除奖励检查不通过
				return &pb.S2C_TimeStoneUseMessage{Code: 31}
			}
			blackHole := player.BlackHole
			old := data.Node
			_, index, _ := lo.FindIndexOf(blackHole.Map, func(n *structs.BlackHoleNode) bool { return n.Id == old.Id })
			if index == -1 {
				// 找不到原来的旧节点?
				return &pb.S2C_TimeStoneUseMessage{Code: 32}
			}
			deduct = append(deduct, r...)
			blackHole.CurId = data.CurId
			blackHole.NextId = data.NextId
			blackHole.Roles = array.SliceCopy(data.Roles)
			blackHole.Buffs = array.SliceCopy(data.Buffs)
			blackHole.Aids = array.SliceCopy(data.Aids)
			blackHole.Equips = array.SliceCopy(data.Equips)
			blackHole.Deads = array.SliceCopy(data.Deads)
			blackHole.Team = array.SliceCopy(data.Team)
			blackHole.Map[index] = old
			old.BlackHole = blackHole
			respData, _ = anypb.New(data.ToDetailPb())
		case pb.TimeStoneEvent_TypeEquipMake:
			data := evt.GetEvent().(*structs.EquipMakeEvent)
			data.Expired = true
			eq := player.Equip.Get(data.Uid)
			if eq == nil {
				// 回溯时找不到装备了？
				return &pb.S2C_TimeStoneUseMessage{Code: 21}
			}
			makeBean, _ := cfg.EquipMakeContainer.GetBeanByUnique(data.BeanId)
			if makeBean == nil {
				// 打造台配置找不到了？
				return &pb.S2C_TimeStoneUseMessage{Code: 22}
			}
			conds := structs.ConfigConditionConvert(makeBean.Cost...).All()
			add = append(add, conds...)
			deductFn = append(deductFn, func() {
				player.Equip.Pull(data.Uid)
			})
		}
	}
	player.TimeStone.Energy -= energyCost
	player.DeductCosts(deduct, ta.ResChangeSceneTypeUnknown)
	for _, fn := range deductFn {
		fn()
	}
	player.GrantRewards(add, ta.ResChangeSceneTypeUnknown)
	return &pb.S2C_TimeStoneUseMessage{Code: 0, Extra: respData}
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间

//@logic-code-end
