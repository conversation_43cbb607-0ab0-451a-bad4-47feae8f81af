package game

import (
	"context"
	"math"
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/data/item_id"
	"train/base/enum/character_quality"
	"train/base/enum/user_type"
	"train/base/structs"
	"train/common/pb"
	"train/common/ta"
	"train/db"
	"train/game/cmd"
	ut "train/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/types/known/anypb"
)

// InitMsgHD 自动生成，不要在这个方法添加任何内容。
func InitMsgHD(this *Game) {
	// 获取玩家基础数据
	this.middleware.Wrap("C2S_GetPlayerInfoMessage", this.C2sGetPlayerInfoMessageHandler, "log=false")
	// 执行Gm命令
	this.middleware.Wrap("C2S_GmExecuteMessage", this.C2sGmExecuteMessageHandler)
	// 抽卡消息
	this.middleware.Wrap("C2S_JackpotReqMessage", this.C2sJackpotReqMessageHandler)
	// 收取资源
	this.middleware.Wrap("C2S_CollectItemMessage", this.C2sCollectItemMessageHandler)
	// 开始使用加速
	this.middleware.Wrap("C2S_SpeedUpMessage", this.C2sSpeedUpMessageHandler)
	// 停止使用加速
	this.middleware.Wrap("C2S_StopSpeedUpMessage", this.C2sStopSpeedUpMessageHandler)
	// 同步加速状态
	this.middleware.Wrap("C2S_SyncSpeedUpMessage", this.C2sSyncSpeedUpMessageHandler)
	// 恢复加速能量
	this.middleware.Wrap("C2S_RecoverEnergyMessage", this.C2sRecoverEnergyMessageHandler)
	// 与客户端同步部分数值
	this.middleware.Wrap("C2S_SyncMessage", this.C2sSyncMessageHandler)
	// 记录引导数据关键步骤
	this.middleware.Wrap("C2S_RecordGuideStepMessage", this.C2sRecordGuideStepMessageHandler)
	// 领取任务奖励
	this.middleware.Wrap("C2S_ClaimTaskRewardMessage", this.C2sClaimTaskRewardMessageHandler)
	// 同步当前星球数据
	this.middleware.Wrap("C2S_SyncPlanetMessage", this.C2sSyncPlanetMessageHandler)
	// 同步每日刷新的数据
	this.middleware.Wrap("C2S_SyncDailyInfoMessage", this.C2sSyncDailyInfoMessageHandler)
	// 获取邮件详情
	this.middleware.Wrap("C2S_MailDetailMessage", this.C2sMailDetailMessageHandler)
	// 删除已读
	this.middleware.Wrap("C2S_DeleteReadMailMessage", this.C2sDeleteReadMailMessageHandler)
	// 领取邮件附件
	this.middleware.Wrap("C2S_AttachMailMessage", this.C2sAttachMailMessageHandler)
	// 拉取邮件列表
	this.middleware.Wrap("C2S_MailListMessage", this.C2sMailListMessageHandler)
	// 兑换码
	this.middleware.Wrap("C2S_CheckCdkMessage", this.C2sCheckCdkMessageHandler)
	// 修改个人信息
	this.middleware.Wrap("C2S_DiyPlayerInfoMessage", this.C2sDiyPlayerInfoMessageHandler)
	// 申请注销
	this.middleware.Wrap("C2S_SignOutMessage", this.C2sSignOutMessageHandler)
	// 去除new标签
	this.middleware.Wrap("C2S_RemoveNewMarkMessage", this.C2sRemoveNewMarkMessageHandler)
	// 钻石兑换电池
	this.middleware.Wrap("C2S_BuyBatteryMessage", this.C2sBuyBatteryMessageHandler)
	// 设置战斗编队
	this.middleware.Wrap("C2S_SetBattleTeamMessage", this.C2sSetBattleTeamMessageHandler)
	// 抽卡积分领取
	this.middleware.Wrap("C2S_JackpotPointsGetMessage", this.C2sJackpotPointsGetMessageHandler)
	// 解锁自动加速
	this.middleware.Wrap("C2S_UnlockSpeedUpAutoMessage", this.C2sUnlockSpeedUpAutoMessageHandler)
	// 获取广告奖励
	this.middleware.Wrap("C2S_GetAdRewardMessage", this.C2sGetAdRewardMessageHandler)
	// 同步加速能量
	this.middleware.Wrap("C2S_SyncEnergyMessage", this.C2sSyncEnergyMessageHandler)
}
func (this *Game) C2sGetPlayerInfoMessageHandler(player *structs.Player, msg *pb.C2S_GetPlayerInfoMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	log.Info("I - C2sGetPlayerInfo [%s] [%s]", player.Session.TraceID(), player.GetUid())
	info := player.ToPlayerBaseInfo()
	// if info.NickName == "" || info.AvatarUrl == "" {
	// 	user := player.GetUser()
	// 	if info.NickName == "" {
	// 		info.NickName = user.NickName
	// 	}
	// 	if info.AvatarUrl == "" {
	// 		info.AvatarUrl = user.AvatarUrl
	// 	}
	// }
	log.Info("O - C2sGetPlayerInfo [%s] [%s]", player.Session.TraceID(), player.GetUid())
	return &pb.S2C_GetPlayerInfoResMessage{
		Code:   0,
		Player: info,
	}
	//@action-code-end
}
func (this *Game) C2sGmExecuteMessageHandler(player *structs.Player, msg *pb.C2S_GmExecuteMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	s, e := cmd.GlobalGetGmCmd().Execute(msg.Cmd, player, false)
	if e != nil {
		log.Error("C2sGmExecuteMessageHandler Error :%s", e.Error())
	}
	return &pb.S2C_GmExecuteRspMessage{
		Reply: s,
	}
	//@action-code-end
}
func (this *Game) C2sJackpotReqMessageHandler(player *structs.Player, msg *pb.C2S_JackpotReqMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	Type := cast.ToInt(msg.Type)
	msgId := msg.MsgId

	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_JackpotRspMessage{Code: cast.ToInt32(code)}
	}

	if history := player.JackpotHasRecord(msgId); history != nil {
		return &pb.S2C_JackpotRspMessage{
			Code: 0,
			List: lo.Map(history, func(item *structs.Condition, _ int) *pb.Condition { return item.ToPb() }),
		}
	}
	misc := cfg.GetMisc()

	count := 1
	if Type == 2 {
		count = 10
	}
	// 是不是钻石抽
	isDiamond := false
	// 是不是打折了
	isDiscount := false
	// 判断车票道具
	cs := structs.ToCondition(map[string]interface{}{
		"id":   item_id.TICKET,
		"type": condition.PROP,
		"num":  count,
	})
	failed := player.CheckCondition(cs)
	if !failed {
		isDiamond = true
		total := misc.JackPotPrice * count
		// 十连折扣
		if count == 10 {
			total = cast.ToInt(math.Floor(cast.ToFloat64(total) * misc.JackPotDiscount))
			isDiscount = true
		}
		// 生成条件
		cs = structs.ToCondition(map[string]interface{}{
			"id":   0,
			"type": condition.DIAMOND,
			"num":  total,
		})
		failed = player.CheckCondition(cs)
		// 货币不足,无法抽取
		if !failed {
			return send(2)
		}
	}

	result := player.Jackpot(count, isDiamond, isDiscount)

	player.DeductCost(cs, ta.ResChangeSceneTypeJackpot)
	// 抽卡记录
	player.DoJackpotRecord(msgId, result)

	return &pb.S2C_JackpotRspMessage{
		Code: 0,
		List: lo.Map(result, func(item *structs.Condition, _ int) *pb.Condition { return item.ToPb() }),
	}
	//@action-code-end
}
func (this *Game) C2sCollectItemMessageHandler(player *structs.Player, msg *pb.C2S_CollectItemMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	player.UpdateSpeedUp()
	player.UpdateAllOutput()
	resp := &pb.S2C_CollectItemRespMessage{
		Code: cast.ToInt32(0),
	}
	resp.Carriages = msg.GetCarriages()
	carriageDatas := lo.If(msg.GetCarriages() != nil, msg.GetCarriages()).Else(make([]*pb.Output, 0))
	for _, carriageData := range carriageDatas {
		id := cast.ToInt(carriageData.GetId())
		carriage := player.GetCarriageById(id)
		if carriage == nil {
			continue
		}
		itemDatas := lo.If(carriageData.GetItems() != nil, carriageData.GetItems()).Else(make([]*pb.Condition, 0))
		for _, itemData := range itemDatas {
			item := &structs.Condition{}
			item.FromPb(itemData)
			num := player.ClaimCarriageOutput(carriage, item)
			itemData.Num = cast.ToInt32(num)
		}
	}
	passengerStar := cast.ToInt(msg.GetPassengerStar())
	if passengerStar > 0 {
		passengerStar = player.ClaimPassengerStar(passengerStar)
		resp.PassengerStar = cast.ToInt32(passengerStar)
	}
	heart := cast.ToInt(msg.GetHeart())
	if heart > 0 {
		heart = player.ClaimHeart(heart)
		resp.Heart = cast.ToInt32(heart)
	}
	player.ResetOutputRewardDuration()
	return resp
	//@action-code-end
}
func (this *Game) C2sSpeedUpMessageHandler(player *structs.Player, msg *pb.C2S_SpeedUpMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	player.StartSpeedUp()
	return &pb.S2C_SpeedUpMessage{
		Code:   0,
		Energy: cast.ToInt32(player.Energy.Energy),
		Time:   cast.ToUint64(player.GetWorldTime()),
	}
	//@action-code-end
}
func (this *Game) C2sStopSpeedUpMessageHandler(player *structs.Player, msg *pb.C2S_StopSpeedUpMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	player.StopSpeedUp()
	return &pb.S2C_SpeedUpMessage{
		Code:   0,
		Energy: cast.ToInt32(player.Energy.Energy),
		Time:   cast.ToUint64(player.GetWorldTime()),
	}
	//@action-code-end
}
func (this *Game) C2sSyncSpeedUpMessageHandler(player *structs.Player, msg *pb.C2S_SyncSpeedUpMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	player.UpdateSpeedUp()
	return &pb.S2C_SpeedUpMessage{
		Code:   0,
		Energy: cast.ToInt32(player.Energy.Energy),
		Time:   cast.ToUint64(player.GetWorldTime()),
	}
	//@action-code-end
}
func (this *Game) C2sRecoverEnergyMessageHandler(player *structs.Player, msg *pb.C2S_RecoverEnergyMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	code := RecoverEnergy(player, int(msg.Type))
	return &pb.S2C_RecoverEnergyRespMessage{Code: cast.ToInt32(code), Energy: player.EnergyToPb()}
	//@action-code-end
}
func (this *Game) C2sSyncMessageHandler(player *structs.Player, msg *pb.C2S_SyncMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	return &pb.S2C_SyncMessage{
		ElectricTime: int32(player.Train.GetElectricTime()),
	}
	//@action-code-end
}
func (this *Game) C2sRecordGuideStepMessageHandler(player *structs.Player, msg *pb.C2S_RecordGuideStepMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	// 做一些简单的数据验证，保证数据基本正确才报错
	code := 0
	if msg.StepId/100 != msg.GuideId {
		code = 1 // 数据不对
	}
	_, exists := cfg.GuideContainer.GetBeanById(cast.ToInt(msg.StepId))
	if !exists {
		code = 2 // 步骤id不对
	}
	// 判断是不是关键步骤
	if code == 0 {
		player.RecordGuideInfo(cast.ToInt(msg.GuideId), cast.ToInt(msg.StepId))
	}
	return &pb.S2C_RecordGuideStepResultMessage{
		Code: cast.ToInt32(code),
	}
	//@action-code-end
}
func (this *Game) C2sClaimTaskRewardMessageHandler(player *structs.Player, msg *pb.C2S_ClaimTaskRewardMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	id := msg.Id
	json, exists := cfg.TaskContainer.GetBeanByUnique(id)
	if !exists {
		return &pb.S2C_ClaimTaskRewardResultMessage{Code: 1}
	}
	task := player.GetCurTask(id)
	if task == nil {
		return &pb.S2C_ClaimTaskRewardResultMessage{Code: 2}
	}
	if player.IsTaskCompleted(task.Id) {
		return &pb.S2C_ClaimTaskRewardResultMessage{Code: 3}
	}
	if !player.CheckTaskDone(task) {
		return &pb.S2C_ClaimTaskRewardResultMessage{Code: 4}
	}
	player.CompleteTask(task.Id)
	rewards := structs.ToConditions(json.Reward)
	player.GrantRewards(rewards, ta.ResChangeSceneTypeTask)
	player.CheckTaskSpReward(task.GetJson().Mark)
	return &pb.S2C_ClaimTaskRewardResultMessage{Code: 0}
	//@action-code-end
}
func (this *Game) C2sSyncPlanetMessageHandler(player *structs.Player, msg *pb.C2S_SyncPlanetMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	planet := player.PlanetData.GetCurPlanet()
	return &pb.S2C_SyncPlanetMessage{Code: 0, Planet: planet.ToPb()}
	//@action-code-end
}
func (this *Game) C2sSyncDailyInfoMessageHandler(player *structs.Player, msg *pb.C2S_SyncDailyInfoMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	sync := player.CheckAndUpdateDaily()
	reply := &pb.S2C_SyncDailyInfoRespMessage{
		Code:                1, //不同步数据
		NextDaySurpluTime:   int32(player.GetNextDaySurplusTime()),
		NextWeekSurplusTime: int32(player.GetNextWeekSurplusTime()),
	}
	if sync {
		reply.Code = 0
		reply.JackpotDailyNum = int32(player.JackpotMod.JackpotDailyNum)
		reply.Energy = player.EnergyToPb()
		reply.BlackHole = player.BlackHole.ToPb()
		reply.Store = player.Store.ToPb()
		reply.ArrestData = player.ArrestModule.ToPb()
		reply.DailyTask = player.DailyTask.ToPb()
		reply.Transport = player.Transport.ToPb()
		reply.SpaceStone = player.SpaceStone.ToPb()
		reply.OfflineRewardTime = int32(player.OutputRewardDuration)
	}
	return reply
	//@action-code-end
}
func (this *Game) C2sMailDetailMessageHandler(player *structs.Player, msg *pb.C2S_MailDetailMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	mail := player.DetailMail(msg.MailId)
	reply := &pb.S2C_MailDetailRespMessage{
		Mail: nil,
		Code: 1,
	}
	if mail != nil {
		reply.Mail = mail.TpPbMailInfo(true)
		reply.Code = 0
	}
	return reply
	//@action-code-end
}
func (this *Game) C2sDeleteReadMailMessageHandler(player *structs.Player, msg *pb.C2S_DeleteReadMailMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	player.DeleteReadMail()
	return &pb.S2C_DeleteReadMailRespMessage{
		Code: 0,
	}
	//@action-code-end
}
func (this *Game) C2sAttachMailMessageHandler(player *structs.Player, msg *pb.C2S_AttachMailMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	mailId := msg.MailId
	code, conditions, ids := player.AttachMail(mailId)
	return &pb.S2C_AttachMailRespMessage{
		Code:    cast.ToInt32(code),
		Rewards: conditions,
		Ids:     ids,
	}
	//@action-code-end
}
func (this *Game) C2sMailListMessageHandler(player *structs.Player, msg *pb.C2S_MailListMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	player.InitMailModule()
	switch msg.Type {
	case 1:
		for _, mail := range player.Mail.List {
			if !mail.Attach || !mail.Read {
				return &pb.S2C_MailListRespMessage{Mail: []*pb.MailInfo{mail.TpPbMailInfo(false)}}
			}
		}
	default:
		return &pb.S2C_MailListRespMessage{Mail: player.ListToPbMailInfo(true)}
	}
	return &pb.S2C_MailListRespMessage{Mail: make([]*pb.MailInfo, 0)}
	//@action-code-end
}
func (this *Game) C2sCheckCdkMessageHandler(player *structs.Player, msg *pb.C2S_CheckCdkMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	c, rewards := player.UseCdk(msg.Cdk)
	replay := &pb.S2C_CheckCdkMessage{
		Code: c,
	}
	replay.Rewards = structs.ToPbConditions(rewards)
	return replay
	//@action-code-end
}
func (this *Game) C2sDiyPlayerInfoMessageHandler(player *structs.Player, msg *pb.C2S_DiyPlayerInfoMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	replay := &pb.S2C_DiyPlayerInfoRespMessage{}
	if !ut.IsEmpty(msg.NickName) {
		l := lo.RuneLength(msg.NickName)
		if l > 8 || l < 2 {
			replay.Code = 1
			return replay
		}

		// 一分钟只能改一次
		if player.GetNowTime()-player.LastEditNameTime < ut.TIME_MINUTE {
			replay.Code = 3
			return replay
		}

		single := db.PLAYER.GetCollection().FindOne(context.TODO(), &bson.M{
			"nickName": msg.NickName,
		}, options.FindOne().SetProjection(&bson.M{"id": 1}))
		// 重复的名称
		if single.Err() != mongo.ErrNoDocuments {
			replay.Code = 4
			return replay
		}

		player.LastEditNameTime = player.GetNowTime()
		cnt := player.GetChangeNameCount()
		renameCfg := cfg.Misc_CContainer.GetObj().Renamed
		if cnt >= renameCfg.FreeNum {
			// 扣除改名费用
			cost := &structs.Condition{Type: condition.DIAMOND, Id: -1, Num: renameCfg.BuyPrice}
			if !player.CheckCondition(cost) {
				// 消耗不足
				replay.Code = 2
				return replay
			}
			player.DeductCost(cost, ta.ResChangeSceneTypeUnknown)
		}
		// 改名
		player.ChangeNickName(msg.NickName)
	}
	return replay
	//@action-code-end
}
func (this *Game) C2sSignOutMessageHandler(player *structs.Player, msg *pb.C2S_SignOutMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	replay := &pb.S2C_SignOutRespMessage{}
	user := structs.GetUserFromDbById(player.Id)
	if user == nil || user.UserType == user_type.GUEST {
		// 是游客 不能注销
		replay.Code = 1
		return replay
	}
	// 检查认证信息 暂无
	user.EnterSignOut()
	return replay
	//@action-code-end
}
func (this *Game) C2sRemoveNewMarkMessageHandler(player *structs.Player, msg *pb.C2S_RemoveNewMarkMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	typeId := cast.ToInt(msg.GetTypeId())
	aryVal := cast.ToIntSlice(msg.GetAryVal())
	player.RemoveNew(typeId, aryVal)
	return &pb.S2C_RemoveNewMarkMessage{Code: cast.ToInt32(0)}
	//@action-code-end
}
func (this *Game) C2sBuyBatteryMessageHandler(player *structs.Player, msg *pb.C2S_BuyBatteryMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	num := cast.ToInt(msg.GetNum())
	misc := cfg.Misc_CContainer.GetObj()
	cost := misc.SpeedUp.RecoverEnergy * num
	cond := &structs.Condition{Type: condition.DIAMOND, Num: cost}
	if !player.CheckCondition(cond) {
		return &pb.S2C_BuyBatteryMessage{Code: 1}
	}
	player.GrantReward(&structs.Condition{Type: condition.PROP, Id: item_id.ENERGY, Num: num}, ta.ResChangeSceneTypeBattery)
	player.DeductCost(cond, ta.ResChangeSceneTypeBag)
	return &pb.S2C_BuyBatteryMessage{Code: 0}
	//@action-code-end
}
func (this *Game) C2sSetBattleTeamMessageHandler(player *structs.Player, msg *pb.C2S_SetBattleTeamMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	team := msg.GetTeam()
	if team == nil {
		return &pb.S2C_SetBattleTeamMessage{Code: 1}
	}
	id := cast.ToInt(team.Id)
	uids := team.GetUids()
	if len(uids) > 5 {
		return &pb.S2C_SetBattleTeamMessage{Code: 2}
	}
	if id == 10 {
		player.BlackHole.Team = uids
	} else {
		player.Battle.SetTeam(id, uids)
	}
	return &pb.S2C_SetBattleTeamMessage{Code: 0}
	//@action-code-end
}
func (this *Game) C2sJackpotPointsGetMessageHandler(player *structs.Player, msg *pb.C2S_JackpotPointsGetMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	jackpotMod := player.JackpotMod
	needPoints := cfg.GetMisc().JackpotPointsGet
	if needPoints > jackpotMod.JackpotPoints {
		return &pb.S2C_JackpotPointsGetMessage{Code: 1}
	}
	jackpotMod.JackpotPoints -= needPoints
	pool := cfg.CharacterContainer.GetData()
	characters := lo.Filter(pool, func(character *cfg.Character[int], i int) bool {
		return character.Quality == character_quality.ORANGE
	})
	result := make([]*structs.Condition, 0)
	passenger := player.RandomPassenger(characters...)
	result = append(result, player.CheckChangePassenger(passenger, result))
	player.GrantRewards(result, ta.ResChangeSceneTypeJackpot)
	return &pb.S2C_JackpotPointsGetMessage{Code: 0, List: structs.ToPbConditions(result)}
	//@action-code-end
}
func (this *Game) C2sUnlockSpeedUpAutoMessageHandler(player *structs.Player, msg *pb.C2S_UnlockSpeedUpAutoMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	mod := player.Energy
	if mod.IsUnlockSpeedUpAuto {
		return &pb.S2C_UnlockSpeedUpAutoMessage{Code: 1}
	}
	misc := cfg.Misc_CContainer.GetObj()

	unlockAutoCost := structs.ConfigConditionConvert(misc.SpeedUp.UnlockAutoCost...)
	failed := player.CheckConditions(unlockAutoCost.All())
	if len(failed) > 0 {
		return &pb.S2C_UnlockSpeedUpAutoMessage{Code: 2}
	}
	player.DeductCosts(unlockAutoCost.All(), ta.ResChangeSceneTypeUnknown)
	// 解锁自动
	mod.IsUnlockSpeedUpAuto = true
	// 补差能量
	// mod.Energy += misc.SpeedUp.Max * ut.TIME_SECOND

	return &pb.S2C_UnlockSpeedUpAutoMessage{}
	//@action-code-end
}
func (this *Game) C2sGetAdRewardMessageHandler(player *structs.Player, msg *pb.C2S_GetAdRewardMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间

	// 检查次数
	if player.Ad.GetRemainTimes(msg.GetType()) <= 0 {
		return &pb.S2C_GetAdRewardMessage{Code: 1}
	}
	var replay *anypb.Any
	switch msg.GetType() {
	case pb.AdType_RecoveryTrainEnergy:
		player.Energy.RecoverEnergy()
		replay, _ = anypb.New(player.EnergyToPb())
	case pb.AdType_RecoveryOreBreakItem:
		oreConfig := cfg.Misc_CContainer.GetObj().Ore
		player.GrantReward(&structs.Condition{Type: condition.PROP, Id: item_id.OreBreakItem, Num: oreConfig.BreakMaxNum}, ta.ResChangeSceneTypeOre)
	default:
		// 未知的广告类型
		return &pb.S2C_GetAdRewardMessage{Code: 2}
	}
	player.Ad.DeductTimes(msg.GetType())
	return &pb.S2C_GetAdRewardMessage{Data: replay}
	//@action-code-end
}

func (this *Game) C2sSyncEnergyMessageHandler(player *structs.Player, msg *pb.C2S_SyncEnergyMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	return &pb.S2C_SyncEnergyMessage{Energy: player.EnergyToPb()}
	//@action-code-end
}

// @logic-code-start 自定义代码必须放在start和end之间
func RecoverEnergy(player *structs.Player, _type int) int {
	Energy := player.Energy
	// 1免费 2电池 3广告
	switch _type {
	case 1:
		if Energy.FreeRecoverNum <= 0 {
			return 1
		}
		Energy.FreeRecoverNum--
		Energy.RecoverEnergy()
	case 3:
		if player.Ad.GetRemainTimes(pb.AdType_RecoveryTrainEnergy) <= 0 {
			return 4
		}
		player.Ad.DeductTimes(pb.AdType_RecoveryTrainEnergy)
		Energy.RecoverEnergy()
	default:
		cost := 1
		var cond *structs.Condition
		misc := cfg.GetMisc()
		if _type == 2 {
			cond = &structs.Condition{Type: condition.DIAMOND, Num: cost * misc.SpeedUp.RecoverEnergy}
		}
		if cond == nil {
			return 3
		}
		if !player.CheckCondition(cond) {
			return 2
		}
		player.DeductCost(cond, ta.ResChangeSceneTypeNoReport)
		player.Energy.RecoverEnergy()
	}
	return 0
}

//@logic-code-end
