package game

import (
	"train/base/cfg"
	"train/base/event"
	"train/base/structs"
	"train/common/pb"
	"train/common/ta"
	"train/utils/array"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitTowerHD 自动生成，不要在这个方法添加任何内容。
func InitTowerHD(this *Game) {
	// 爬塔战斗
	this.middleware.Wrap("C2S_TowerBattleMessage", this.C2sTowerBattleMessageHandler)
	// 战斗胜利(临时)
	this.middleware.Wrap("C2S_TowerBattleWinMessage", this.C2sTowerBattleWinMessageHandler)
}
func (this *Game) C2sTowerBattleMessageHandler(player *structs.Player, msg *pb.C2S_TowerBattleMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	return &pb.S2C_TowerBattleMessage{Code: 0}
	//@action-code-end
}
func (this *Game) C2sTowerBattleWinMessageHandler(player *structs.Player, msg *pb.C2S_TowerBattleWinMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	tower := player.Tower
	beans := cfg.TowerMonsterContainer.GetData()
	bean := array.Find(beans, func(b *cfg.TowerMonster[string]) bool { return b.Id == tower.CheckPointId })
	rewards := structs.ToConditions(bean.Reward)
	player.GrantRewards(rewards, ta.ResChangeSceneTypeTower)
	tower.ToNext()
	player.GetEvent().Emit(event.TowerComplete)
	return &pb.S2C_TowerBattleWinMessage{Code: 0, Tower: tower.ToPb()}
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间
//@logic-code-end
