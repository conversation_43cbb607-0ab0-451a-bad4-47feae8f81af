package task

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/manager"
	"train/base/structs"
	"train/db"
	"train/db/lua"

	"github.com/bamzi/jobrunner"
	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var isRunning = false

var (
	title = "竞技场奖励"
)

// 赛季时长
func SeasonDay() time.Duration {
	return time.Duration(cfg.Misc_CContainer.GetObj().Pvp[0].Duration) * time.Hour
}

func (t *Task) RunTick() {
	isRunning = true
	t.GoogleOrderRefundCheck()

	// 这个库使用标准的cron表达式格式
	// 包含5个字段:
	// 分钟 小时 日期 月份 星期
	err := jobrunner.Schedule("0 5 * * *", &NewDayTicker{taskModule: t})

	if err != nil {
		log.Error("Schedule error: %v", err)
	}

	// <-time.After(time.Second * 20)
	// i := &NewDayTicker{taskModule: t}
	// i.Run()
}

func (t *Task) StopTick() { isRunning = false }

type NewDayTicker struct {
	taskModule *Task
}

func (n *NewDayTicker) Run() {
	log.Info("run.....")

	// 获取赛季结算倒计时
	seasonKey := db.RKPvpNormalRankSeason()
	ttl := db.GetRedis().TTL(context.TODO(), seasonKey).Val()
	isSeasonEnd := ttl.Seconds() <= 0

	nextSeason := SeasonDay() - time.Hour
	data, _ := db.GetRedis().HGetAll(context.TODO(), db.RKServerArea()).Result()
	var wg sync.WaitGroup
	ary := lo.Map(lo.Keys(data), func(item string, index int) int { return cast.ToInt(item) })
	// 先快照 之后的操作都是基于这个快照
	n.backupPvpRank(ary)
	// 开始结算
	for _, i := range ary {
		wg.Add(1)
		go func(sid int) {
			defer wg.Done()
			// 结算奖励
			n.pvpNormalReward(sid, isSeasonEnd)
			if isSeasonEnd {
				// 直接删除当前赛季排行 分数有一个单独的key存放
				db.GetRedis().Del(context.TODO(), db.RKPvpNormalRank(sid))
				// 下一次赛季结算时间
				db.GetRedis().Set(context.TODO(), seasonKey, "1", nextSeason)
			}
		}(i)
	}
	wg.Wait()
}

// 每次结算前 先备份pvp排行 容错
func (n *NewDayTicker) backupPvpRank(sidAry []int) {
	log.Debug("结算前备份pvp排行开始... [%v]", sidAry)

	// 限制并发数量，避免资源耗尽
	semaphore := make(chan struct{}, 10) // 最多10个并发
	var wg sync.WaitGroup
	var mu sync.Mutex
	failedServers := make([]int, 0)

	for _, i := range sidAry {
		wg.Add(1)
		semaphore <- struct{}{} // 获取信号量

		go func(sid int) {
			defer func() {
				<-semaphore // 释放信号量
				wg.Done()
			}()

			if err := n.backupSinglePvpRank(sid); err != nil {
				mu.Lock()
				failedServers = append(failedServers, sid)
				mu.Unlock()
				log.Error("备份区服 %d 排行榜失败: %v", sid, err)
			}
		}(i)
	}

	wg.Wait()

	if len(failedServers) > 0 {
		log.Error("以下区服备份失败: %v", failedServers)
	}
	log.Debug("结算前备份pvp排行结束...")
}

// 备份单个区服的PVP排行榜
func (n *NewDayTicker) backupSinglePvpRank(sid int) error {
	rankKey := db.RKPvpNormalRank(sid)
	backupKey := db.RKPvpNormalRankTodayBackup(sid)

	// 使用Lua脚本进行备份
	result, err := db.GetRedis().Eval(context.TODO(),
		lua.BackupPvpRankScript,
		[]string{rankKey, backupKey},
		int(time.Hour*24/time.Second), // 24小时过期
	).Result()

	if err != nil {
		return fmt.Errorf("执行备份脚本失败: %w", err)
	}

	resultArray := result.([]interface{})
	code := int(resultArray[0].(int64))
	if code != 0 {
		return fmt.Errorf("备份失败: %s", resultArray[2].(string))
	}

	memberCount := int(resultArray[1].(int64))
	log.Debug("区服 %d 备份成功，共 %d 个成员", sid, memberCount)
	return nil
}

// 结算普通竞技场pvp奖励
func (n *NewDayTicker) pvpNormalReward(serverId int, season bool) {
	pvp := cfg.Misc_CContainer.GetObj().Pvp
	if len(pvp) == 0 {
		return
	}
	// 普通竞技场奖励
	data := pvp[0]

	log.Debug("结算普通竞技场pvp奖励开始, 是否同时结算赛季奖励:%t", season)
	// 从快照里面取
	rankKey := db.RKPvpNormalRankTodayBackup(serverId)
	total := db.GetRedis().ZCard(context.TODO(), rankKey).Val()
	log.Debug("结算普通竞技场pvp奖励, 区服:%d, 总数:%d", serverId, total)
	var sort int64 = 0
	for {
		if total <= 0 {
			break
		}

		sort += 1
		total -= 10000
		// 每次只拉 10000 个 出错重试5次
		retry := 5
		for {
			if retry <= 0 {
				break
			}
			retry--
			cmd := db.GetRedis().ZRevRange(context.TODO(), rankKey, (sort-1)*10000, (sort-1)*10000+9999)
			if e := cmd.Err(); e != nil {
				log.Error("拉取竞技场数据出错:%v, 区服:%d, 重试次数:%d", e, serverId, retry)
				continue
			}
			ary := cmd.Val()
			operations := make([]mongo.WriteModel, 0)

			for j, id := range ary {
				num := data.GetRewardNum(j + 1)
				if num <= 0 {
					continue
				}
				// 机器人
				if strings.HasPrefix(id, "robot_") {
					continue
				}
				// 普通奖励
				content := fmt.Sprintf("恭喜您在昨天的竞技场结算时排名第%d,这是您的奖励。", j+1)
				operation := sendMail(n.taskModule, id, title, content, []*structs.Condition{
					{
						Type: condition.DIAMOND,
						Id:   -1,
						Num:  num,
					},
				})
				if operation != nil {
					operations = append(operations, operation)
				}
				// 赛季奖励
				if season {
					content = fmt.Sprintf("恭喜您在上个赛季竞技场结算时排名第%d,这是您的奖励。", j+1)
					num = data.GetSeasonRewardNum(j + 1)
					operation = sendMail(n.taskModule, id, title, content, []*structs.Condition{
						{
							Type: condition.DIAMOND,
							Id:   -1,
							Num:  num,
						},
					})
					if operation != nil {
						operations = append(operations, operation)
					}
					// 赛季重置分数
					plrDataKey := db.RKPvpNormalDataOf(id)
					db.GetRedis().Eval(context.TODO(),
						lua.PvpSeasonResetScript,
						[]string{plrDataKey},
						id,
					)
				}
				if len(operations) >= 1000 || j == len(ary)-1 {
					_, er := db.MAIL.GetCollection().BulkWrite(context.TODO(), operations, options.BulkWrite().SetOrdered(true))
					if er != nil {
						log.Error("竞技场批量结算邮件写入出错: %v", er.Error())
					}
					operations = operations[:0]
				}
			}
			break
		}
	}

	log.Debug("结算普通竞技场pvp奖励结束, 是不是赛季:%t", season)
}

func sendMail(module *Task, id string, title, content string, rewards []*structs.Condition) mongo.WriteModel {
	// 在线玩家发放
	nodeId, err := manager.GlobalGetPlayerManager().GetPlayerNodeId(id)
	if err == nil && nodeId != "" {
		bytes, _ := json.Marshal(&rewards)
		result, estr := module.Invoke(nodeId, "/httpSendMail", id, title, content, string(bytes))
		if estr == "" && result != nil {
			if vm, ok := result.(map[string]interface{}); ok && vm != nil {
				// 发送成功!
				if code, ok := vm["code"]; ok && code.(float64) == 0 {
					return nil
				}
			}
		}
	}

	// 在线玩家发放失败 or 不在线 写入批量待更新
	mail := structs.NewMail(title, content, id, rewards)
	return mongo.NewInsertOneModel().
		SetDocument(&mail)
}
