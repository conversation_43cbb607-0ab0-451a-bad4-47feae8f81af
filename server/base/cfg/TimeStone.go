package cfg

const ConstTimeStone = "TimeStone"

type TimeStone[K int] struct {
	Id           K                        `json:"id"`
	DailyRecover int                      `json:"dailyRecover"`
	Energy       int                      `json:"energy"`
	EventMax     int                      `json:"eventMax"`
	BuyCost      []map[string]interface{} `json:"buyCost"`
}

func (t *TimeStone[K]) GetName() string { return ConstTimeStone }
func (t *TimeStone[K]) GetUnique() K    { return t.Id }
func (t *TimeStone[K]) OnInit() {
}
