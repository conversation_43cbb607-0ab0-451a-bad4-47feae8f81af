package cfg

import (
	"github.com/huyangv/vmqant/log"
	"time"
)

var AchievementContainer *ConfigContainer[string, *Achievement[string]]
var AchievementTypeContainer *ConfigContainer[int, *AchievementType[int]]
var ArrestContainer *ConfigContainer[int, *Arrest[int]]
var ArrestLevelContainer *ConfigContainer[int, *ArrestLevel[int]]
var ArrestScoreContainer *ConfigContainer[int, *ArrestScore[int]]
var BattleSkillContainer *ConfigContainer[int, *BattleSkill[int]]
var BlackHoleAddContainer *ConfigContainer[string, *BlackHoleAdd[string]]
var BlackHoleEquipContainer *ConfigContainer[int, *BlackHoleEquip[int]]
var BlackHoleEquipLevelContainer *ConfigContainer[int, *BlackHoleEquipLevel[int]]
var BlackHoleLayerContainer *ConfigContainer[string, *BlackHoleLayer[string]]
var BlackHoleLevelContainer *ConfigContainer[int, *BlackHoleLevel[int]]
var BranchPlanetMapContainer *ConfigContainer[string, *BranchPlanetMap[string]]
var BranchPlanetMineContainer *ConfigContainer[string, *BranchPlanetMine[string]]
var BranchPlanetMonsterContainer *ConfigContainer[string, *BranchPlanetMonster[string]]
var BranchPlanetSpContainer *ConfigContainer[string, *BranchPlanetSp[string]]
var BurstTaskItemContainer *ConfigContainer[int, *BurstTaskItem[int]]
var ChapterPlanetMineContainer *ConfigContainer[string, *ChapterPlanetMine[string]]
var ChapterPlanetMonsterContainer *ConfigContainer[string, *ChapterPlanetMonster[string]]
var ChapterPlanetSpContainer *ConfigContainer[string, *ChapterPlanetSp[string]]
var CharacterContainer *ConfigContainer[int, *Character[int]]
var CharacterFragContainer *ConfigContainer[int, *CharacterFrag[int]]
var CharacterLevelCostContainer *ConfigContainer[int, *CharacterLevelCost[int]]
var CharacterPlotControlContainer *ConfigContainer[string, *CharacterPlotControl[string]]
var CharacterProfileContainer *ConfigContainer[int, *CharacterProfile[int]]
var CharacterSkinContainer *ConfigContainer[string, *CharacterSkin[string]]
var ChestContainer *ConfigContainer[int, *Chest[int]]
var CollectMineContainer *ConfigContainer[int, *CollectMine[int]]
var DailyTaskContainer *ConfigContainer[int, *DailyTask[int]]
var DailyTaskDialogContainer *ConfigContainer[int, *DailyTaskDialog[int]]
var DailyTaskItemContainer *ConfigContainer[int, *DailyTaskItem[int]]
var DailyTaskRewardContainer *ConfigContainer[int, *DailyTaskReward[int]]
var DynamicStepContainer *ConfigContainer[int, *DynamicStep[int]]
var EquipContainer *ConfigContainer[int, *Equip[int]]
var EquipEffectContainer *ConfigContainer[int, *EquipEffect[int]]
var EquipLevelContainer *ConfigContainer[int, *EquipLevel[int]]
var EquipMakeContainer *ConfigContainer[string, *EquipMake[string]]
var EquipSectionContainer *ConfigContainer[int, *EquipSection[int]]
var EquipStoreContainer *ConfigContainer[int, *EquipStore[int]]
var FieldLevelContainer *ConfigContainer[int, *FieldLevel[int]]
var FieldSeedContainer *ConfigContainer[int, *FieldSeed[int]]
var GrowSkillContainer *ConfigContainer[string, *GrowSkill[string]]
var GuideContainer *ConfigContainer[int, *Guide[int]]
var InstanceContainer *ConfigContainer[int, *Instance[int]]
var InstanceLevelContainer *ConfigContainer[int, *InstanceLevel[int]]
var ItemContainer *ConfigContainer[int, *Item[int]]
var JackpotContainer *ConfigContainer[int, *Jackpot[int]]
var Misc_CContainer *ConfigContainer[string, *Misc_C[string]]
var MonsterCampContainer *ConfigContainer[string, *MonsterCamp[string]]
var MonsterStarUpContainer *ConfigContainer[int, *MonsterStarUp[int]]
var NpcContainer *ConfigContainer[int, *Npc[int]]
var OffContainer *ConfigContainer[int, *Off[int]]
var OreItemContainer *ConfigContainer[int, *OreItem[int]]
var OreLayerContainer *ConfigContainer[int, *OreLayer[int]]
var OreLevelContainer *ConfigContainer[int, *OreLevel[int]]
var OreMapContainer *ConfigContainer[int, *OreMap[int]]
var OutputDurationContainer *ConfigContainer[int, *OutputDuration[int]]
var PlanetContainer *ConfigContainer[int, *Planet[int]]
var PlanetAreaContainer *ConfigContainer[string, *PlanetArea[string]]
var PlanetBranchContainer *ConfigContainer[string, *PlanetBranch[string]]
var PlanetMapContainer *ConfigContainer[string, *PlanetMap[string]]
var PlanetMineContainer *ConfigContainer[int, *PlanetMine[int]]
var PlanetMonsterContainer *ConfigContainer[int, *PlanetMonster[int]]
var PlanetMoveTimeContainer *ConfigContainer[int, *PlanetMoveTime[int]]
var PlanetProfileContainer *ConfigContainer[int, *PlanetProfile[int]]
var PlanetProfileRewardContainer *ConfigContainer[string, *PlanetProfileReward[string]]
var ProfileBranchLevelContainer *ConfigContainer[int, *ProfileBranchLevel[int]]
var PublicityPlayContainer *ConfigContainer[string, *PublicityPlay[string]]
var ResonanceContainer *ConfigContainer[int, *Resonance[int]]
var RobotEquipContainer *ConfigContainer[int, *RobotEquip[int]]
var ShopContainer *ConfigContainer[string, *Shop[string]]
var SpaceStoneContainer *ConfigContainer[int, *SpaceStone[int]]
var StarUpContainer *ConfigContainer[int, *StarUp[int]]
var StoreContainer *ConfigContainer[string, *Store[string]]
var StoreOffContainer *ConfigContainer[string, *StoreOff[string]]
var TalentAttrContainer *ConfigContainer[int, *TalentAttr[int]]
var TalentAttrLevelContainer *ConfigContainer[int, *TalentAttrLevel[int]]
var TaskContainer *ConfigContainer[string, *Task[string]]
var TimeStoneContainer *ConfigContainer[int, *TimeStone[int]]
var TimeStoneBossContainer *ConfigContainer[int, *TimeStoneBoss[int]]
var TimeStoneEventContainer *ConfigContainer[int, *TimeStoneEvent[int]]
var ToolContainer *ConfigContainer[int, *Tool[int]]
var ToolLevelContainer *ConfigContainer[string, *ToolLevel[string]]
var ToolTableContainer *ConfigContainer[int, *ToolTable[int]]
var TowerMonsterContainer *ConfigContainer[string, *TowerMonster[string]]
var TrainContainer *ConfigContainer[int, *Train[int]]
var TrainActivityItemContainer *ConfigContainer[int, *TrainActivityItem[int]]
var TrainDailyTaskContainer *ConfigContainer[int, *TrainDailyTask[int]]
var TrainDailyTaskConditionContainer *ConfigContainer[string, *TrainDailyTaskCondition[string]]
var TrainDailyTaskItemContainer *ConfigContainer[int, *TrainDailyTaskItem[int]]
var TrainDailyTaskLevelContainer *ConfigContainer[int, *TrainDailyTaskLevel[int]]
var TrainGoodsContainer *ConfigContainer[string, *TrainGoods[string]]
var TrainGoodsLevelContainer *ConfigContainer[string, *TrainGoodsLevel[string]]
var TrainItemContainer *ConfigContainer[string, *TrainItem[string]]
var TrainItemLevelContainer *ConfigContainer[string, *TrainItemLevel[string]]
var TrainTechContainer *ConfigContainer[int, *TrainTech[int]]
var TrainTechLevelContainer *ConfigContainer[string, *TrainTechLevel[string]]
var TrainThemeContainer *ConfigContainer[string, *TrainTheme[string]]
var TransportLevelContainer *ConfigContainer[int, *TransportLevel[int]]
var TransportMonsterContainer *ConfigContainer[string, *TransportMonster[string]]
var UnlockFuncContainer *ConfigContainer[int, *UnlockFunc[int]]
var WantedConditionContainer *ConfigContainer[string, *WantedCondition[string]]
var WantedLevelContainer *ConfigContainer[int, *WantedLevel[int]]

func LoadConfig() {
	sTime := time.Now()
	log.Info("开始初始化配置文件!")
	AchievementContainer = localNewContainer[string, *Achievement[string]](ConstAchievement)
	AchievementTypeContainer = localNewContainer[int, *AchievementType[int]](ConstAchievementType)
	ArrestContainer = localNewContainer[int, *Arrest[int]](ConstArrest)
	ArrestLevelContainer = localNewContainer[int, *ArrestLevel[int]](ConstArrestLevel)
	ArrestScoreContainer = localNewContainer[int, *ArrestScore[int]](ConstArrestScore)
	BattleSkillContainer = localNewContainer[int, *BattleSkill[int]](ConstBattleSkill)
	BlackHoleAddContainer = localNewContainer[string, *BlackHoleAdd[string]](ConstBlackHoleAdd)
	BlackHoleEquipContainer = localNewContainer[int, *BlackHoleEquip[int]](ConstBlackHoleEquip)
	BlackHoleEquipLevelContainer = localNewContainer[int, *BlackHoleEquipLevel[int]](ConstBlackHoleEquipLevel)
	BlackHoleLayerContainer = localNewContainer[string, *BlackHoleLayer[string]](ConstBlackHoleLayer)
	BlackHoleLevelContainer = localNewContainer[int, *BlackHoleLevel[int]](ConstBlackHoleLevel)
	BranchPlanetMapContainer = localNewContainer[string, *BranchPlanetMap[string]](ConstBranchPlanetMap)
	BranchPlanetMineContainer = localNewContainer[string, *BranchPlanetMine[string]](ConstBranchPlanetMine)
	BranchPlanetMonsterContainer = localNewContainer[string, *BranchPlanetMonster[string]](ConstBranchPlanetMonster)
	BranchPlanetSpContainer = localNewContainer[string, *BranchPlanetSp[string]](ConstBranchPlanetSp)
	BurstTaskItemContainer = localNewContainer[int, *BurstTaskItem[int]](ConstBurstTaskItem)
	ChapterPlanetMineContainer = localNewContainer[string, *ChapterPlanetMine[string]](ConstChapterPlanetMine)
	ChapterPlanetMonsterContainer = localNewContainer[string, *ChapterPlanetMonster[string]](ConstChapterPlanetMonster)
	ChapterPlanetSpContainer = localNewContainer[string, *ChapterPlanetSp[string]](ConstChapterPlanetSp)
	CharacterContainer = localNewContainer[int, *Character[int]](ConstCharacter)
	CharacterFragContainer = localNewContainer[int, *CharacterFrag[int]](ConstCharacterFrag)
	CharacterLevelCostContainer = localNewContainer[int, *CharacterLevelCost[int]](ConstCharacterLevelCost)
	CharacterPlotControlContainer = localNewContainer[string, *CharacterPlotControl[string]](ConstCharacterPlotControl)
	CharacterProfileContainer = localNewContainer[int, *CharacterProfile[int]](ConstCharacterProfile)
	CharacterSkinContainer = localNewContainer[string, *CharacterSkin[string]](ConstCharacterSkin)
	ChestContainer = localNewContainer[int, *Chest[int]](ConstChest)
	CollectMineContainer = localNewContainer[int, *CollectMine[int]](ConstCollectMine)
	DailyTaskContainer = localNewContainer[int, *DailyTask[int]](ConstDailyTask)
	DailyTaskDialogContainer = localNewContainer[int, *DailyTaskDialog[int]](ConstDailyTaskDialog)
	DailyTaskItemContainer = localNewContainer[int, *DailyTaskItem[int]](ConstDailyTaskItem)
	DailyTaskRewardContainer = localNewContainer[int, *DailyTaskReward[int]](ConstDailyTaskReward)
	DynamicStepContainer = localNewContainer[int, *DynamicStep[int]](ConstDynamicStep)
	EquipContainer = localNewContainer[int, *Equip[int]](ConstEquip)
	EquipEffectContainer = localNewContainer[int, *EquipEffect[int]](ConstEquipEffect)
	EquipLevelContainer = localNewContainer[int, *EquipLevel[int]](ConstEquipLevel)
	EquipMakeContainer = localNewContainer[string, *EquipMake[string]](ConstEquipMake)
	EquipSectionContainer = localNewContainer[int, *EquipSection[int]](ConstEquipSection)
	EquipStoreContainer = localNewContainer[int, *EquipStore[int]](ConstEquipStore)
	FieldLevelContainer = localNewContainer[int, *FieldLevel[int]](ConstFieldLevel)
	FieldSeedContainer = localNewContainer[int, *FieldSeed[int]](ConstFieldSeed)
	GrowSkillContainer = localNewContainer[string, *GrowSkill[string]](ConstGrowSkill)
	GuideContainer = localNewContainer[int, *Guide[int]](ConstGuide)
	InstanceContainer = localNewContainer[int, *Instance[int]](ConstInstance)
	InstanceLevelContainer = localNewContainer[int, *InstanceLevel[int]](ConstInstanceLevel)
	ItemContainer = localNewContainer[int, *Item[int]](ConstItem)
	JackpotContainer = localNewContainer[int, *Jackpot[int]](ConstJackpot)
	Misc_CContainer = localNewContainer[string, *Misc_C[string]](ConstMisc_C)
	MonsterCampContainer = localNewContainer[string, *MonsterCamp[string]](ConstMonsterCamp)
	MonsterStarUpContainer = localNewContainer[int, *MonsterStarUp[int]](ConstMonsterStarUp)
	NpcContainer = localNewContainer[int, *Npc[int]](ConstNpc)
	OffContainer = localNewContainer[int, *Off[int]](ConstOff)
	OreItemContainer = localNewContainer[int, *OreItem[int]](ConstOreItem)
	OreLayerContainer = localNewContainer[int, *OreLayer[int]](ConstOreLayer)
	OreLevelContainer = localNewContainer[int, *OreLevel[int]](ConstOreLevel)
	OreMapContainer = localNewContainer[int, *OreMap[int]](ConstOreMap)
	OutputDurationContainer = localNewContainer[int, *OutputDuration[int]](ConstOutputDuration)
	PlanetContainer = localNewContainer[int, *Planet[int]](ConstPlanet)
	PlanetAreaContainer = localNewContainer[string, *PlanetArea[string]](ConstPlanetArea)
	PlanetBranchContainer = localNewContainer[string, *PlanetBranch[string]](ConstPlanetBranch)
	PlanetMapContainer = localNewContainer[string, *PlanetMap[string]](ConstPlanetMap)
	PlanetMineContainer = localNewContainer[int, *PlanetMine[int]](ConstPlanetMine)
	PlanetMonsterContainer = localNewContainer[int, *PlanetMonster[int]](ConstPlanetMonster)
	PlanetMoveTimeContainer = localNewContainer[int, *PlanetMoveTime[int]](ConstPlanetMoveTime)
	PlanetProfileContainer = localNewContainer[int, *PlanetProfile[int]](ConstPlanetProfile)
	PlanetProfileRewardContainer = localNewContainer[string, *PlanetProfileReward[string]](ConstPlanetProfileReward)
	ProfileBranchLevelContainer = localNewContainer[int, *ProfileBranchLevel[int]](ConstProfileBranchLevel)
	PublicityPlayContainer = localNewContainer[string, *PublicityPlay[string]](ConstPublicityPlay)
	ResonanceContainer = localNewContainer[int, *Resonance[int]](ConstResonance)
	RobotEquipContainer = localNewContainer[int, *RobotEquip[int]](ConstRobotEquip)
	ShopContainer = localNewContainer[string, *Shop[string]](ConstShop)
	SpaceStoneContainer = localNewContainer[int, *SpaceStone[int]](ConstSpaceStone)
	StarUpContainer = localNewContainer[int, *StarUp[int]](ConstStarUp)
	StoreContainer = localNewContainer[string, *Store[string]](ConstStore)
	StoreOffContainer = localNewContainer[string, *StoreOff[string]](ConstStoreOff)
	TalentAttrContainer = localNewContainer[int, *TalentAttr[int]](ConstTalentAttr)
	TalentAttrLevelContainer = localNewContainer[int, *TalentAttrLevel[int]](ConstTalentAttrLevel)
	TaskContainer = localNewContainer[string, *Task[string]](ConstTask)
	TimeStoneContainer = localNewContainer[int, *TimeStone[int]](ConstTimeStone)
	TimeStoneBossContainer = localNewContainer[int, *TimeStoneBoss[int]](ConstTimeStoneBoss)
	TimeStoneEventContainer = localNewContainer[int, *TimeStoneEvent[int]](ConstTimeStoneEvent)
	ToolContainer = localNewContainer[int, *Tool[int]](ConstTool)
	ToolLevelContainer = localNewContainer[string, *ToolLevel[string]](ConstToolLevel)
	ToolTableContainer = localNewContainer[int, *ToolTable[int]](ConstToolTable)
	TowerMonsterContainer = localNewContainer[string, *TowerMonster[string]](ConstTowerMonster)
	TrainContainer = localNewContainer[int, *Train[int]](ConstTrain)
	TrainActivityItemContainer = localNewContainer[int, *TrainActivityItem[int]](ConstTrainActivityItem)
	TrainDailyTaskContainer = localNewContainer[int, *TrainDailyTask[int]](ConstTrainDailyTask)
	TrainDailyTaskConditionContainer = localNewContainer[string, *TrainDailyTaskCondition[string]](ConstTrainDailyTaskCondition)
	TrainDailyTaskItemContainer = localNewContainer[int, *TrainDailyTaskItem[int]](ConstTrainDailyTaskItem)
	TrainDailyTaskLevelContainer = localNewContainer[int, *TrainDailyTaskLevel[int]](ConstTrainDailyTaskLevel)
	TrainGoodsContainer = localNewContainer[string, *TrainGoods[string]](ConstTrainGoods)
	TrainGoodsLevelContainer = localNewContainer[string, *TrainGoodsLevel[string]](ConstTrainGoodsLevel)
	TrainItemContainer = localNewContainer[string, *TrainItem[string]](ConstTrainItem)
	TrainItemLevelContainer = localNewContainer[string, *TrainItemLevel[string]](ConstTrainItemLevel)
	TrainTechContainer = localNewContainer[int, *TrainTech[int]](ConstTrainTech)
	TrainTechLevelContainer = localNewContainer[string, *TrainTechLevel[string]](ConstTrainTechLevel)
	TrainThemeContainer = localNewContainer[string, *TrainTheme[string]](ConstTrainTheme)
	TransportLevelContainer = localNewContainer[int, *TransportLevel[int]](ConstTransportLevel)
	TransportMonsterContainer = localNewContainer[string, *TransportMonster[string]](ConstTransportMonster)
	UnlockFuncContainer = localNewContainer[int, *UnlockFunc[int]](ConstUnlockFunc)
	WantedConditionContainer = localNewContainer[string, *WantedCondition[string]](ConstWantedCondition)
	WantedLevelContainer = localNewContainer[int, *WantedLevel[int]](ConstWantedLevel)
	log.Info("配置文件初始化完成， during:%fs", time.Since(sTime).Seconds())
}

