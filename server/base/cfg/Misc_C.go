package cfg

import (
	ut "train/utils"

	"github.com/samber/lo"
)

const ConstMisc_C = "Misc_C"

type Misc_C[K string] struct {
	RefreshTimeStr            string `json:"refreshTime"`
	RefreshTimePmStr          string `json:"refreshTimePm"`
	RefreshTime               int
	RefreshTimePm             int
	PlanetMoveTimeToStoneUnit int                      `json:"planetMoveTimeToStoneUnit"`
	Gift                      []map[string]interface{} `json:"gift"`
	JackpotHistoryMaxNum      int                      `json:"jackpotHistoryMaxNum"`
	JackpotDailyMaxNum        int                      `json:"jackpotDailyMaxNum"`
	Jackpot                   struct {
		MustGetCharacter []interface{} `json:"mustGetCharacter"`
		DiffNum          int           `json:"diffNum"`
	} `json:"jackpot"`
	JackPotPrice         int            `json:"jackPotPrice"`
	JackPotDiscount      float64        `json:"jackPotDiscount"`
	JackpotPointsConvert int            `json:"jackpotPointsConvert"`
	JackpotPointsGet     int            `json:"jackpotPointsGet"`
	RobotGemPrice        []int          `json:"robotGemPrice"`
	QteDamage            map[string]int `json:"qteDamage"`

	SpeedUp *SpeedUp `json:"speedUp"`

	Weight         []map[string]int `json:"weight"`
	TimeTransition int              `json:"timeTransition"` //现实时间和游戏时间的转换比
	GemShop        []map[string]int `json:"gemShop"`

	GuideDevelop struct {
		Function    string `json:"function"`
		CharacterId int    `json:"characterId"`
		CurrentLv   int    `json:"currentLv"`
		TargetLv    int    `json:"targetLv"`
	} `json:"guideDevelop"`

	GuideReward []struct {
		GuideId int `json:"guideId"`
		Type    int `json:"type"`
		Id      int `json:"id"`
		Num     int `json:"num"`
	} `json:"guideReward"`

	INITIAL       []map[string]interface{} `json:"INITIAL"`
	TipExchange   int                      `json:"tipExchange"`
	HeartExchange int                      `json:"heartExchange"`
	ScoreMax      map[string]int           `json:"scoreMax"`
	ToolBaseSpeed float64                  `json:"toolBaseSpeed"`
	Character     struct {
		MoveSpeed    int     `json:"moveSpeed"`
		RageModeRate float64 `json:"rageModeRate"`
	} `json:"character"`
	ScoreRank []struct {
		Min float64 `json:"min"`
		Max float64 `json:"max"`
	} `json:"scoreRank"` // 潜力数值划分的等级
	ToolBuild   []*ToolBuildWeight `json:"toolBuild"`   // 工具打造的类型权重分布
	ToolInitial []*ToolInitial     `json:"toolInitial"` // 初始工具
	Guide       *struct {
		HeadstockStarNum  int `json:"headstockStarNum"` //车头喝茶小费
		DeleteBlueprintId int `json:"deleteBlueprintId"`
	} `json:"guide"`
	Renamed *RenameCfg `json:"renamed"` // 改名配置

	PowerSpeedUpEffect float64        `json:"powerSpeedUpEffect"`
	PowerSpeedUp       []*SpeedUpCost `json:"powerSpeedUp"`
	WaterSpeedUpEffect float64        `json:"waterSpeedUpEffect"`
	WaterSpeedUp       []*SpeedUpCost `json:"waterSpeedUp"`

	WorkTime struct {
		Start string `json:"start"`
		End   string `json:"end"`
	}

	CheckInOutputRate float64 `json:"checkInOutputRate"`
	WorkOutputRate    float64 `json:"workOutputRate"`

	TowerPower SpeedUp `json:"towerPower"`

	BlackHole struct {
		BaseAttr int `json:"baseAttr"`
		// Store    struct {
		// 	RefreshCost  int `json:"refreshCost"`
		// 	RefreshPrice int `json:"refreshPrice"`
		// } `json:"store"`
	} `json:"blackHole"`

	InstanceTimes  int `json:"instanceTimes"` // 每日副本开采次数
	InstanceFinish *struct {
		Time int `json:"time"`
	} `json:"instanceFinish"` // 每日副本开采使用钻石立即完成的cost  (1钻石 = Time分钟,已经转换成毫秒)
	RebirthCD     int `json:"rebirthCD"` // 乘客重置CD  分钟
	RebirthFinish *struct {
		Time int `json:"time"`
	} `json:"rebirthFinish"` // 乘客重置CD 时间转换钻石消耗单位
	StoreRefresh *struct {
		Num   int `json:"num"`
		Price int `json:"price"`
	} `json:"storeRefresh"` // 商店刷新次数和价格

	Stores []*StoreConfig `json:"stores"`

	Wanted *struct {
		RefreshCost  int   `json:"refreshCost"`
		InitialLevel []int `json:"InitialLevel"`
	} `json:"wanted"`

	Resonance ResonanceCost `json:"resonance"`

	LvAttrRatio     float64 `json:"lvAttrRatio"`
	StarLvAttrRatio float64 `json:"starLvAttrRatio"`

	Field struct {
		FoodLandUnlockCost  []int `json:"foodLandUnlockCost"`
		FruitLandUnlockCost []int `json:"fruitLandUnlockCost"`
		FoodLandUnlock      int   `json:"foodLandUnlock"`
		FruitLandUnlock     int   `json:"fruitLandUnlock"`
	} `json:"field"`

	Fertilizer []*FertilizerEffectConfig `json:"fertilizer"`
	Talent     *TalentConfig             `json:"talent"`
	Transport  *TransportConfig          `json:"transport"`
	FragUp     []int                     `json:"fragUp"`
	Ore        *OreConfig                `json:"ore"`
	Equip      struct {
		Sell float64 `json:"sell"`
	} `json:"equip"`
	DailyTask *DailyTaskConfig `json:"dailyTask"`
	Collect   *CollectConfig   `json:"collect"`
	Arrest    *ArrestConfig    `json:"arrest"`

	SpaceStone *SpaceStoneConfig `json:"spaceStone"`

	Ticket         *TicketConfig      `json:"ticket"`
	FieldUnlockGet []*ConfigCondition `json:"fieldUnlockGet"`
	Pvp            []*PvpCfg          `json:"pvp"`

	DeepExplore *DeepExploreConfig `json:"deepExplore"`
	Ad          []*AdConfig        `json:"ad"`

	ProfileBranch *ProfileBranchConfig `json:"profileBranch"`

	TimeBox       []*TimeBoxConfig     `json:"timeBox"`
	RandomBox     []*RandomBoxConfig   `json:"randomBox"`
	TrainActivity *TrainActivityConfig `json:"trainActivity"`
}

func (this *Misc_C[K]) GetName() string { return ConstMisc_C }

func (this *Misc_C[K]) GetUnique() K { return K(ConstMisc_C) }

func (this *Misc_C[K]) OnInit() {
	this.RefreshTime = ut.StringToTime(this.RefreshTimeStr)
	this.RefreshTimePm = ut.StringToTime(this.RefreshTimePmStr)
	if this.InstanceFinish != nil {
		this.InstanceFinish.Time = this.InstanceFinish.Time * ut.TIME_MINUTE
	}
	if this.RebirthFinish != nil {
		this.RebirthFinish.Time = this.RebirthFinish.Time * ut.TIME_MINUTE
	}
	// 防止配置错误，这里至少是1倍
	this.Transport.TimeRate = ut.Max(this.Transport.TimeRate, 1)
}

func (this *Misc_C[K]) RandomToolBuildType() *ToolBuildWeight {
	gs := make([]ut.DataWeight, 0)
	for _, weight := range this.ToolBuild {
		gs = append(gs, weight)
	}
	return gs[ut.RandomIndexByDataWeight(gs)].(*ToolBuildWeight)
}

// GetPassengerResetCdTime
/*
 * @description : 获取乘客重置CD时间
 * @return int
 */
func (this *Misc_C[K]) GetPassengerResetCdTime() int {
	return this.RebirthCD * ut.TIME_MINUTE
}

// GetFertilizerTime
/*
 * @description : 获取肥料的效果时间
 * @param id
 * @return int 时间已经转换成毫秒了
 */
func (this *Misc_C[K]) GetFertilizerTime(id int) int {
	bean, _ := lo.Find(this.Fertilizer, func(config *FertilizerEffectConfig) bool {
		return config.Id == id
	})
	if bean == nil {
		return 0
	}
	return bean.Effect * ut.TIME_MINUTE
}

type SpeedUp struct {
	Max            int                `json:"max"`
	RecoverFree    int                `json:"recoverFree"`
	RecoverBuy     int                `json:"recoverBuy"`
	RecoverSpeed   int                `json:"recoverSpeed"`
	RecoverEnergy  int                `json:"recoverEnergy"`
	S1             int                `json:"s1"`
	S2             int                `json:"s2"`
	S3             int                `json:"s3"`
	S6             int                `json:"s6"`
	S8             int                `json:"s8"`
	UnlockAutoCost []*ConfigCondition `json:"unlockAutoCost"`
	AutoMax        int                `json:"autoMax"`
}

type ToolBuildWeight struct {
	Type   int `json:"type"`
	Weight int `json:"weight"`
}

func (t *ToolBuildWeight) GetWeight() int {
	return t.Weight
}

type ToolInitial struct {
	Lv      int                      `json:"lv"`
	Type    int                      `json:"type"`
	BuyCost []map[string]interface{} `json:"buyCost"`
}

type Monster struct {
	Id     int `json:"id"`
	Level  int `json:"lv"`
	StarLv int `json:"starLv"`
}

type RenameCfg struct {
	FreeNum  int `json:"freeNum"`
	BuyPrice int `json:"buyPrice"`
}

type SpeedUpCost struct {
	Cost int `json:"cost"`
	Time int `json:"time"`
}

type ResonanceCost struct {
	CdCost int `json:"cdCost"`
	Cd     int `json:"cd"`
}

type StoreConfig struct {
	RefreshNum   int `json:"refreshNum"`
	RefreshPrice int `json:"refreshPrice"`
	RefreshTime  int `json:"refreshTime"`
}

type SpaceStoneConfig struct {
	Cost int `json:"cost"`
}
type TicketConfig struct {
	MergeCnt int `json:"mergeCnt"`
}

type DeepExploreConfig struct {
	SpaceshipCost   []int `json:"spaceshipCost"`
	SpaceshipMaxNum int   `json:"spaceshipMaxNum"`
}

type AdConfig struct {
	Count     int    `json:"count"`
	ResetType string `json:"resetType"`
}

type ProfileBranchConfig struct {
	MaxEnergy   int `json:"maxEnergy"`
	RecoverTime int `json:"recoverTime"`
}

type TimeBoxConfig struct {
	Id      int            `json:"id"`
	Time    float64        `json:"time"`
	Rewards []*ChestReward `json:"reward"`
}

type RandomBoxConfig struct {
	Id           int            `json:"id"`
	Rewards      []*ChestReward `json:"reward"`
	RewardRandom []*ChestReward `json:"rewardRandom"`
}

type TrainActivityConfig struct {
	DailyCntMin  int            `json:"dailyCntMin"`
	DailyCntMax  int            `json:"dailyCntMax"`
	RewardRandom []*ChestReward `json:"rewardRandom"`
}
