package cfg

const ConstShop = "Shop"

type Shop[K string] struct {
	ID         K              `json:"id"`
	Type       int            `json:"type"`
	SimpleDesc string         `json:"simpleDesc"`
	Day        int            `json:"day"`
	ProductId  string         `json:"productId"`
	Rmb        float64        `json:"rmb"`
	FirstRmb   float64        `json:"firstRmb"`
	Product    []*ChestReward `json:"product"`
	First      []*ChestReward `json:"first"`
	Gifts      []*ChestReward `json:"gifts"`
	Daily      []*ChestReward `json:"daily"`
}

func (this *Shop[K]) GetName() string { return ConstShop }

func (this *Shop[K]) GetUnique() K { return this.ID }

func (this *Shop[K]) OnInit() {
}
