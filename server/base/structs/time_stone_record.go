package structs

import (
	"train/common/pb"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/types/known/anypb"
)

type TimeStoneEvent interface {
	GetType() pb.TimeStoneEvent
	ToPb() protoreflect.ProtoMessage
	CanUse() bool
}

type TimeStoneEventWrapper struct {
	Type                 pb.TimeStoneEvent     `bson:"type"`
	JackpotEvent         *JackpotEvent         `bson:"data,omitempty"`
	EquipMakeEvent       *EquipMakeEvent       `bson:"equipMakeData,omitempty"`
	BlackHoleBattleEvent *BlackHoleBattleEvent `bson:"blackHoleBattleData,omitempty"`
}

func (w *TimeStoneEventWrapper) GetEvent() TimeStoneEvent {
	switch w.Type {
	case pb.TimeStoneEvent_TypeJackpot:
		return w.JackpotEvent
	case pb.TimeStoneEvent_TypeEquipMake:
		return w.EquipMakeEvent
	case pb.TimeStoneEvent_TypeBlackHoleBattle:
		return w.BlackHoleBattleEvent
	}
	log.Error("TimeStoneEventWrapper GetEvent error, type: %d", w.Type)
	return nil
}

func (w *TimeStoneEventWrapper) UnionData(data TimeStoneEvent) {
	switch data.GetType() {
	case pb.TimeStoneEvent_TypeJackpot:
		w.JackpotEvent = data.(*JackpotEvent)
	case pb.TimeStoneEvent_TypeEquipMake:
		w.EquipMakeEvent = data.(*EquipMakeEvent)
	case pb.TimeStoneEvent_TypeBlackHoleBattle:
		w.BlackHoleBattleEvent = data.(*BlackHoleBattleEvent)
	default:
		log.Error("TimeStoneEventWrapper UnionData error, type: %d", data.GetType())
	}
}

func (w *TimeStoneEventWrapper) ToPb() *pb.TimeStoneEventWrapper {
	pbData := &pb.TimeStoneEventWrapper{
		Type: w.Type,
		Data: nil,
	}
	event := w.GetEvent()
	if event != nil {
		data, _ := anypb.New(event.ToPb())
		pbData.Data = data
	}
	return pbData
}

/* 抽卡邀请 start */
type JackpotEvent struct {
	DrawType               int  `bson:"drawType"`               // 1车票 2钻石
	DrawPid                int  `bson:"drawPid"`                // 乘客id
	IsConvertIntoFragments bool `bson:"isConvertIntoFragments"` // 是否转换成碎片了
	IsDiamondDiscount      bool `bson:"isDiamondDiscount"`      // 钻石抽取是不是折扣
	Expired                bool `bson:"expired"`                // 是否过期
}

func (j *JackpotEvent) GetType() pb.TimeStoneEvent { return pb.TimeStoneEvent_TypeJackpot }
func (j *JackpotEvent) CanUse() bool               { return !j.Expired }

func (j *JackpotEvent) ToPb() protoreflect.ProtoMessage {
	return &pb.JackpotEventData{
		DrawType:               int32(j.DrawType),
		DrawPid:                int32(j.DrawPid),
		IsConvertIntoFragments: j.IsConvertIntoFragments,
		IsDiamondDiscount:      j.IsDiamondDiscount,
		Expired:                j.Expired,
	}
}

/* 抽卡邀请 end */

/* 装备打造 start */
type EquipMakeEvent struct {
	Uid     string `bson:"uid"`     // 打造结果的装备id
	EquipId int    `bson:"equipId"` // 打造结果的装备id
	BeanId  string `bson:"beanId"`  // 打造配置id
	Expired bool   `bson:"expired"` // 是否过期
}

func (e *EquipMakeEvent) GetType() pb.TimeStoneEvent { return pb.TimeStoneEvent_TypeEquipMake }
func (e *EquipMakeEvent) CanUse() bool               { return !e.Expired }

func (e *EquipMakeEvent) ToPb() protoreflect.ProtoMessage {
	return &pb.EquipMakeEventData{
		Uid:     e.Uid,
		EquipId: int32(e.EquipId),
		BeanId:  e.BeanId,
		Expired: e.Expired,
	}
}

/* 装备打造 end */

/* 星海迷宫战斗 start */
type BlackHoleBattleEvent struct {
	CurId   string            `bson:"curId"`
	NextId  string            `bson:"nextId"`
	Roles   []*BattleRole     `bson:"roles"`
	Buffs   []*BlackHoleBuff  `bson:"buffs"`
	Aids    []*BattleRole     `bson:"aids"`
	Equips  []*BlackHoleEquip `bson:"equips"`
	Deads   []string          `bson:"deads"`
	Team    []string          `bson:"team"`
	Node    *BlackHoleNode    `bson:"node"`
	Expired bool              `bson:"expired"` // 是否过期
}

func (b *BlackHoleBattleEvent) GetType() pb.TimeStoneEvent {
	return pb.TimeStoneEvent_TypeBlackHoleBattle
}
func (b *BlackHoleBattleEvent) CanUse() bool { return !b.Expired }

// 这里只返回简要数据  客户端不需要详细的数据
func (b *BlackHoleBattleEvent) ToPb() protoreflect.ProtoMessage {
	return &pb.BlackHoleBattleEventData{
		Layer:   int32(b.Node.Layer),
		Level:   int32(b.Node.Level),
		CurId:   b.CurId,
		Expired: b.Expired,
	}
}

func (b *BlackHoleBattleEvent) ToDetailPb() protoreflect.ProtoMessage {
	return &pb.BlackHoleBattleEventData{
		Layer:   int32(b.Node.Layer),
		Level:   int32(b.Node.Level),
		CurId:   b.CurId,
		NextId:  b.NextId,
		Roles:   lo.Map(b.Roles, func(e *BattleRole, i int) *pb.BattleRole { return e.ToPb() }),
		Buffs:   lo.Map(b.Buffs, func(e *BlackHoleBuff, i int) *pb.BlackHoleBuff { return e.ToPb() }),
		Aids:    lo.Map(b.Aids, func(e *BattleRole, i int) *pb.BattleRole { return e.ToPb() }),
		Equips:  lo.Map(b.Equips, func(e *BlackHoleEquip, i int) *pb.BlackHoleEquip { return e.ToPb() }),
		Deads:   b.Deads,
		Team:    &pb.BattleTeam{Id: 10, Uids: b.Team},
		Node:    b.Node.ToPb(),
		Expired: b.Expired,
	}
}

/* 星海迷宫战斗 end */
