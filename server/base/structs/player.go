package structs

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"math"
	"sync"
	"time"
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/data/item_id"
	"train/base/enum"
	"train/base/enum/build_attr"
	"train/base/enum/dynamic_trigger_function_type"
	"train/base/enum/dynamic_trigger_type"
	"train/base/enum/function_type"
	"train/base/enum/output_duration_check_type"
	"train/base/event"
	"train/base/script"
	comm "train/common"
	"train/common/pb"
	"train/common/ta"
	"train/db"
	"train/db/lua"
	ut "train/utils"
	"train/utils/array"

	"google.golang.org/protobuf/reflect/protoreflect"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// buffer对象池
var bufferPool = sync.Pool{
	New: func() interface{} {
		// 256kb
		return bytes.NewBuffer(make([]byte, 0, 256*1024))
	},
}

// gzip对象池
var gzipPool = sync.Pool{
	New: func() interface{} {
		gz, _ := gzip.NewWriterLevel(ioutil.Discard, gzip.BestSpeed)
		return gz
	},
}

type Player struct {
	Uid                        *ut.MongoId              `bson:"-" json:"-"`                 // 内存数据,角色id 使用了mongodb的自增id
	IsNew                      bool                     `bson:"-" json:"-"`                 // 内存数据,是不是新创建的玩家,这个值在创建完成为ture,玩家登陆后就会立即变成false
	Session                    gate.Session             `bson:"-" json:"-"`                 // 内存数据,会话通道
	Destroy                    bool                     `bson:"-" json:"-"`                 // 内部数据，如果这个字段为true，说明数据已不可用，不处理后续请求
	Id                         string                   `bson:"id" json:"-"`                // 这是用户id 表示这个角色属于哪个用户
	NickName                   string                   `bson:"nickName"`                   // 游戏名
	AvatarUrl                  string                   `bson:"avatarUrl"`                  // 游戏头像
	CreateTime                 int                      `bson:"createTime"`                 // 创建时间
	LastLoginTime              int                      `bson:"lastLoginTime"`              // 上一次登录时间
	LastSaveTime               int                      `bson:"lastSaveTime"`               // 上一次保存时间
	LastLogoutTime             int                      `bson:"lastLogoutTime"`             // 上一次登出时间
	LastUpdateTime             int                      `bson:"lastUpdateTime"`             // 上一次刷新时间
	LastWeekUpdateTime         int                      `bson:"lastWeekUpdateTime"`         // 上一次周刷新时间
	Diamond                    int                      `bson:"diamond"`                    // 钻石数量
	AccTotalDiamond            int                      `bson:"accTotalDiamond"`            // 累计钻石数量
	Heart                      int                      `bson:"heart"`                      // 爱心数量
	AccTotalHeart              int                      `bson:"accTotalHeart"`              // 累计爱心数量
	StarDust                   int                      `bson:"starDust"`                   // 星尘数量
	AccTotalStarDust           int                      `bson:"accTotalStarDust"`           // 累计星尘数量
	GM                         bool                     `bson:"gm"`                         // 是不是gm
	Bag                        []*Item                  `bson:"bag"`                        // 背包数据
	Train                      *Train                   `bson:"train"`                      // 车厢数据
	JackpotMod                 *JackpotMod              `bson:"jackpotMod"`                 // 抽卡模块
	Passenger                  []*Passenger             `bson:"passenger"`                  // 乘客数据
	Energy                     *AutoRecoverEnergy       `bson:"energy"`                     // 体力
	PlanetData                 *PlanetData              `bson:"planetData"`                 // 探索数据
	SpeedUpTime                int                      `bson:"speedTime"`                  // 加速时间总计数，记得是游戏时间
	Task                       *PlayerTask              `bson:"Task"`                       // 玩家任务数据
	WorldTime                  int                      `bson:"worldTime"`                  // 累计的游戏世界时间
	LastUpdateWorldTime        int                      `bson:"lastUpdateWorldTime"`        // 最近一次更新世界时间的时间戳
	GuideId                    int                      `bson:"guideId"`                    // 当前引导模块id
	GuideList                  []*GuideInfo             `bson:"guideList"`                  // 引导数据
	eventCenter                *event.Events            `bson:"-"`                          //事件中心
	Tool                       *ToolModel               `bson:"tool"`                       //工具模块
	Explore                    *ExploreModule           `bson:"explore"`                    //探索模块
	DataUser                   *User                    `bson:"-" json:"-"`                 // 用户账号基本数据
	LastLeaveReportTime        time.Time                `bson:"-" json:"-"`                 // 上一次离线上报数据的时间
	CloseGuide                 bool                     `bson:"-" json:"-"`                 // 是否关闭新手教程
	CloseUnlockFunc            bool                     `bson:"-" json:"-"`                 // 是否关闭功能解锁
	Mail                       *MailModule              `bson:"-" json:"-"`                 // 邮件模块 内存数据
	TotalOnlineTime            int                      `bson:"totalOnlineTime"`            // 总在线时长
	ChangeNameCnt              int                      `bson:"changeNameCnt"`              // 改名次数
	Achievement                *Achievement             `bson:"achievement"`                // 成就
	NewMarkList                []*NewMark               `bson:"newMarkList"`                // new标签
	OffsetTime                 int                      `bson:"offsetTime"`                 // 玩家游戏时间偏移
	Chest                      *ChestModule             `bson:"chest"`                      // 宝箱模块
	PassengerStarOutput        float64                  `bson:"passengerStarOutput"`        //入住乘客加成产出部分，算到乘客小费里
	Tower                      *Tower                   `bson:"tower"`                      //爬塔
	BlackHole                  *BlackHole               `bson:"blackHole"`                  //黑洞
	Battle                     *Battle                  `bson:"battle"`                     //战斗模块
	PassengerResonances        []int                    `bson:"passengerResonances"`        //乘客共鸣阵容
	UnlockFuncs                []*cfg.UnlockFunc[int]   `bson:"-" json:"-"`                 //已解锁功能
	Equip                      *EquipModule             `bson:"equip"`                      //装备模块
	Instance                   *InstanceModule          `bson:"instance"`                   //岛屿副本模块
	Wanted                     *WantedModule            `bson:"wanted"`                     //悬赏模块
	PassengerRestCdTime        int                      `bson:"passengerRestCdTime"`        //上一次乘客重置时间
	Store                      *StoreMod                `bson:"store"`                      //商店模块
	Skins                      map[int][]*PassengerSkin `bson:"skins"`                      // 皮肤数据 没解锁的不会放进去
	Pay                        *PayModule               `bson:"pay"`                        //付费数据
	Transport                  *TransportModule         `bson:"transport"`                  //运送模块
	FieldModule                *FieldModule             `bson:"fieldModule"`                //农场模块
	FragItems                  map[int]int              `bson:"fragItems"`                  //投影数据
	Ore                        *OreModule               `bson:"ore"`                        //矿洞模块
	Collect                    *CollectModule           `bson:"collect"`                    // 采集
	TimeStone                  *TimeStone               `bson:"timeStone"`                  // 时间宝石
	SpaceStone                 *SpaceStone              `bson:"spaceStone"`                 // 空间宝石
	ArrestModule               *ArrestModule            `bson:"arrest"`                     // 通缉
	DailyTask                  *DailyTaskModule         `bson:"dailyTask"`                  // 每日任务
	PassengerProfiles          []int                    `bson:"passengerProfiles"`          //乘客资料
	PlanetProfiles             []int                    `bson:"planetProfiles"`             //星球资料
	Resonance                  *Resonance               `bson:"resonance"`                  //共鸣
	AvgLv                      int                      `bson:"avgLv"`                      //乘客平均等级
	Version                    int                      `bson:"version"`                    //存档版本控制
	OutputRewardDuration       int                      `bson:"outputRewardDuration"`       // 产出奖励更新的时长
	LastOutputRewardUpdateTime int                      `bson:"lastOutputRewardUpdateTime"` // 上次产出奖励更新
	AccCount                   map[string]int           `bson:"accCount"`                   //总数记录
	Pvp                        *PvpModule               `bson:"pvpModule"`                  // 竞技场
	ServerId                   int                      `bson:"serverId"`                   // 区服id
	Ad                         *AdModule                `bson:"ad"`                         // 广告
	ProfileBranch              *ProfileBranch           `bson:"profileBranch"`              // 记忆阁
	LastEditNameTime           int                      `bson:"-"`                          // 上次改名时间
	TrainDailyTask             *TrainDailyTaskModule    `bson:"trainDailyTask"`             // 列车日常
	BurstTask                  *BurstTaskModule         `bson:"burstTask"`                  // 突发任务
	DynamicStepTriggered       []int                    `bson:"dynamicStepTriggered"`       // 动态步骤
	TrainActivity              *TrainActivityModule     `bson:"trainActivity"`              // 列车活动
	Tech                       *TechModule              `bson:"tech"`                       // 科技
}

func (plr *Player) GetUid() string {
	return plr.Id
}

// GetSid 获取玩家所在区服
func (plr *Player) GetSid() int {
	return cast.ToInt(plr.Session.Get(enum.PlayerSid))
}

// GetDistanceId 获取数数访客id
func (plr *Player) GetDistanceId() string {
	return plr.Session.Get(enum.DistanceId)
}

// GetClientVersion 获取客户端版本
func (plr *Player) GetClientVersion() string { return plr.Session.Get(enum.ClientVersion) }

// GetPlatform 获取客户端平台
func (plr *Player) GetPlatform() string { return plr.Session.Get(enum.Platform) }

// GetClientVersion 获取客户端版本
func (plr *Player) GetOs() string {
	return plr.Session.Get(enum.OS)
}

func (plr *Player) GetUser() *User {
	if plr.DataUser == nil {
		plr.DataUser = GetUserFromDbById(plr.GetUid())
	}
	return plr.DataUser
}

func (plr *Player) GetEvent() *event.Events {
	return plr.eventCenter
}

// updateOutputRewardDuration 更新奖励产出累计时长
//
// Parameters:
//   - addTime int 额外增加的时长
//
// Returns:
//   - increase int 本次更新增加的时长
func (plr *Player) updateOutputRewardDuration() (increase int) {
	now := plr.GetNowTime()
	if plr.LastOutputRewardUpdateTime == 0 {
		plr.LastOutputRewardUpdateTime = now
	}
	addTime := now - plr.LastOutputRewardUpdateTime
	plr.LastOutputRewardUpdateTime = now
	if !plr.isRealTime() || plr.Train.GetAttrByCond(&Condition{Type: condition.STAR_DUST}) <= 0 {
		return addTime
	}
	return plr.AddOutputDuration(addTime)
}

func (plr *Player) AddOutputDuration(addTime int) (increase int) {
	maxOfflineTime := GetMaxOfflineTime(plr)
	max := maxOfflineTime - plr.OutputRewardDuration
	addTime = ut.Min(max, addTime)
	plr.OutputRewardDuration += addTime
	return addTime
}

func (plr *Player) ResetOutputRewardDuration() {
	plr.OutputRewardDuration = 0
}

// Online 会重新绑定会话
func (plr *Player) Online(session gate.Session) {
	if plr.Session != nil && plr.Session == session {
		return
	}

	plr.fixVersion()

	plr.eventCenter = event.NewEvents()
	plr.Session = session

	if session.Get(enum.CloseGuide) != "" {
		plr.CloseGuide = true
	}
	if session.Get(enum.CloseUnlockFunc) != "" {
		plr.CloseUnlockFunc = true
	}

	now := plr.GetNowTime()
	plr.LastLoginTime = now
	plr.LastSaveTime = now

	if plr.LastUpdateWorldTime == 0 {
		plr.LastUpdateWorldTime = plr.CreateTime
	}
	plr.LastUpdateWorldTime = ut.Max(plr.LastUpdateWorldTime, now-GetMaxOfflineTime(plr))
	plr.UpdateWorldTime()

	if plr.AccCount == nil {
		plr.AccCount = make(map[string]int)
	}

	if plr.Train == nil {
		plr.Train = NewTrainData(plr)
	}
	if plr.Task == nil {
		plr.Task = NewPlayerTaskData()
	}
	if plr.Explore == nil {
		plr.Explore = NewExploreModule()
	}
	if plr.Energy == nil {
		plr.Energy = NewAutoRecoverEnergy()
	}
	if plr.PlanetData == nil {
		plr.PlanetData = NewDefaultPlanetData()
	}
	if plr.Tool == nil {
		plr.Tool = NewToolModel()
	}
	if plr.Achievement == nil {
		plr.Achievement = NewAchievement()
	}
	if plr.Chest == nil {
		plr.Chest = InitChestModule()
	}
	if plr.Tower == nil {
		plr.Tower = NewTower()
	}
	if plr.Battle == nil {
		plr.Battle = NewBattle()
	}
	if plr.BlackHole == nil {
		plr.BlackHole = NewBlackHole()
	}
	if plr.Equip == nil {
		plr.Equip = NewEquipModule()
	}
	if plr.Instance == nil {
		plr.Instance = NewInstanceModule()
	}
	if plr.Wanted == nil {
		plr.Wanted = &WantedModule{}
	}
	if plr.Store == nil {
		plr.Store = NewStoreMod(plr)
	}
	if plr.Pay == nil {
		plr.Pay = NewPayModule()
	}
	if plr.Transport == nil {
		plr.Transport = NewTransportModule()
	}
	if plr.JackpotMod == nil {
		plr.JackpotMod = NewEmptyJackpotMod()
	}

	if plr.FieldModule == nil {
		plr.FieldModule = NewEmptyFieldModule()
	}

	if plr.FragItems == nil {
		plr.FragItems = make(map[int]int)
	}
	if plr.Ore == nil {
		plr.Ore = NewOreModule(plr)
	}
	if plr.Collect == nil {
		plr.Collect = NewCollectModule(plr)
	}

	if plr.TimeStone == nil {
		plr.TimeStone = NewTimeStone()
	}

	if plr.ArrestModule == nil {
		plr.ArrestModule = NewArrestModule()
	}

	if plr.SpaceStone == nil {
		plr.SpaceStone = NewSpaceStone()
	}

	if plr.DailyTask == nil {
		plr.DailyTask = NewDailyTaskModule()
	}

	if plr.Resonance == nil {
		plr.Resonance = NewResonance()
	}

	if plr.Pvp == nil {
		plr.Pvp = NewPvpModule()
	}
	if plr.Ad == nil {
		plr.Ad = NewAdModule()
	}
	if plr.ProfileBranch == nil {
		plr.ProfileBranch = NewProfileBranch()
	}

	if plr.TrainDailyTask == nil {
		plr.TrainDailyTask = NewTrainDailyTaskModule()
	}
	if plr.BurstTask == nil {
		plr.BurstTask = NewBurstTaskModule()
	}
	if plr.TrainActivity == nil {
		plr.TrainActivity = NewTrainActivityModule()
	}
	if plr.Tech == nil {
		plr.Tech = NewTechModule()
	}
	// fix
	if plr.Skins != nil {
		for _, v := range plr.Skins {
			for _, skin := range v {
				if skin.Index == 0 {
					skin.Index = 1
				}
			}
		}
	}
	if plr.Passenger != nil {
		for _, passenger := range plr.Passenger {
			if passenger.UseSkinIndex == 0 {
				passenger.UseSkinIndex = 1
			}
		}
	}

	if len(plr.Skins) == 0 {
		if plr.Skins == nil {
			plr.Skins = make(map[int][]*PassengerSkin)
		}
		// 解锁默认皮肤 并设置穿戴状态
		for _, passenger := range plr.Passenger {
			skinId := fmt.Sprintf("%d-1", passenger.Id)
			bean, exists := cfg.CharacterSkinContainer.GetBeanByUnique(skinId)
			if exists {
				skin := NewPassengerSkin(bean.Index)
				data := make([]*PassengerSkin, 0)
				data = append(data, skin)
				plr.Skins[bean.CharacterId] = data
				passenger.UseSkinIndex = bean.Index
			}
		}
	}
	if plr.IsNew {
		misc := cfg.Misc_CContainer.GetObj()
		init := misc.INITIAL
		conditions := ToConditions(init)
		conditions = lo.Filter(conditions, func(c *Condition, i int) bool {
			return c.Type != condition.PASSENGER
		})
		// 初始道具
		plr.ChangeCostByConds(conditions, 1, ta.ResChangeSceneTypeUnknown)
	}

	//各个模块初始化
	plr.InitItems()
	plr.Tool.Init()
	plr.Resonance.Init(plr)
	plr.InitPassenger()
	plr.PlanetData.Init(plr)
	plr.Explore.Init(plr)
	plr.Energy.Init(plr)
	plr.Train.Init(plr)
	plr.Task.Init(plr)
	plr.Achievement.Init(plr)
	plr.Tower.Init(plr)
	plr.Battle.Init(plr)
	plr.BlackHole.Init(plr)
	plr.Wanted.Init(plr)
	plr.Store.init(plr)
	plr.Equip.Init()
	plr.Transport.init(plr)
	plr.FieldModule.init(plr)
	plr.Ore.Init(plr)
	plr.Instance.init(plr)
	plr.Collect.init(plr)
	plr.TimeStone.init(plr)
	plr.ArrestModule.init(plr)
	plr.DailyTask.init(plr)
	plr.SpaceStone.init(plr)
	plr.Pvp.init(plr)
	plr.Ad.init(plr)
	plr.ProfileBranch.Init(plr)
	plr.TrainDailyTask.init(plr)
	plr.BurstTask.init(plr)
	plr.TrainActivity.init(plr)
	plr.Tech.init(plr)
	plr.Pay.init(plr)

	//初始化一些时间相关的字段
	plr.CheckAndUpdateDaily()
	plr.CheckAndUpdateDailyPm()
	plr.CheckAndUpdateWeek()
	plr.UpdateAllOutput()
	plr.UpdateSpeedUp()

	// @last
	plr.onGlobalListener()
	plr.initUnlockFunction()
	if plr.IsNew {
		// 上报数数
		plr.TaPropertySetAfterCreate()
	}
	// 上报数数进入游戏
	result, _ := plr.Session.Load(enum.IsReconnect)
	plr.TrackEntry(result != "")
}

func (plr *Player) CheckAndUpdateWeek() bool {
	if !plr.CheckRefreshWeek() {
		return false
	}
	plr.LastWeekUpdateTime = plr.GetNowTime()
	// if plr.ArrestModule != nil {
	// 	plr.ArrestModule.CheckAndUpdateResult()
	// }

	return true
}

// CheckAndUpdateDaily 刷新每日相关数据
func (plr *Player) CheckAndUpdateDaily() bool {
	if !plr.CheckRefreshDaily(plr.LastUpdateTime, 1) {
		return false
	}
	plr.LastUpdateTime = plr.GetNowTime()
	// 清零抽卡次数
	plr.RestJackpotDailyCount()

	plr.PlanetData.DailyRefresh()
	// 刷新商店
	if plr.Store != nil {
		plr.Store.DailyUpdateCheck()
	}
	if plr.Energy != nil {
		plr.Energy.Reset()
	}
	if plr.BlackHole != nil {
		plr.BlackHole.CheckAndRefresh()
	}
	if plr.Wanted != nil {
		plr.Wanted.CheckAndRefresh()
	}
	if plr.Transport != nil {
		plr.Transport.CheckAndRefresh()
	}
	if plr.FieldModule != nil {
	}
	if plr.DailyTask != nil {
		plr.DailyTask.CheckAndRefresh()
	}
	if plr.SpaceStone != nil {
		plr.SpaceStone.Refresh()
	}
	if plr.ArrestModule != nil {
		plr.ArrestModule.CheckAndUpdateResult()
	}
	if plr.Ore != nil {
		plr.Ore.UpdateBreakItem(true)
	}
	if plr.Pvp != nil {
		plr.Pvp.ResetTicket(pb.PvpType_NORMAL)
	}
	if plr.Ad != nil {
		plr.Ad.CheckReset("day")
	}
	if plr.TrainDailyTask != nil {
		plr.TrainDailyTask.CheckAndRefresh()
	}
	if plr.TrainActivity != nil {
		plr.TrainActivity.TodayIsRefresh = false
	}
	if plr.TimeStone != nil {
		plr.TimeStone.Refresh()
	}

	log.Info("[%s] Reset daily", plr.GetUid())
	return true
}

// CheckAndUpdateDailyPm
/*
 * @description 每日下午刷新
 * @return bool
 */
func (plr *Player) CheckAndUpdateDailyPm() bool {
	if !plr.CheckRefreshDailyPm(plr.LastUpdateTime, 1) {
		return false
	}
	plr.LastUpdateTime = plr.GetNowTime()
	if plr.Ore != nil {
		plr.Ore.UpdateBreakItem(false)
	}
	plr.Energy.Reset()
	return true
}

func (plr *Player) UpdateWorldTime() int {
	if !plr.isRealTime() {
		return plr.WorldTime
	}
	now := plr.GetNowTime()
	passTime := now - plr.LastUpdateWorldTime
	passWorldTime := TransToWorldTime(passTime)
	plr.WorldTime += passWorldTime
	plr.LastUpdateWorldTime = now
	return plr.WorldTime
}

// GetNowTime 获取真实时间
func (plr *Player) GetNowTime() int {
	return ut.Now() + plr.OffsetTime
}

// GetWorldTime 获取模拟时间
func (plr *Player) GetWorldTime() int {
	return plr.UpdateWorldTime() + plr.SpeedUpTime
}

// 现实时间转游戏时间，单位毫秒
func TransToWorldTime(time int) int {
	miscC := cfg.Misc_CContainer.GetObj()
	timeTransition := miscC.TimeTransition
	return time * timeTransition
}

func TransToRealTime(time int) int {
	miscC := cfg.Misc_CContainer.GetObj()
	timeTransition := miscC.TimeTransition
	return int(math.Round(float64(time) / float64(timeTransition)))
}

// GetMaxOfflineTime  获取玩家产出最大时间
//
// Parameters:
//   - plr *Player
//
// Returns:
//   - int
func GetMaxOfflineTime(plr *Player) int {
	duration := 0
	setDur := func(dur int) {
		duration = ut.Max(dur, duration)
	}

	ary := cfg.OutputDurationContainer.GetData()
	for _, data := range ary {
		switch data.Type {
		case output_duration_check_type.NONE:
			setDur(data.Duration)
		case output_duration_check_type.PASS_PLANET_NODE:
			if len(data.Param) >= 1 {
				nodeId := data.Param[0]
				if plr.PlanetData.isPassNode(nodeId) {
					setDur(data.Duration)
				}
			}
		}
	}
	return duration * ut.TIME_MINUTE
}

func TransCondToBuildAttr(cond *Condition) string {
	switch cond.Type {
	case condition.STAR_DUST:
		return build_attr.STAR
	case condition.HEART:
		return build_attr.HEART
	case condition.PROP:
		if cond.Id == item_id.ELECTRIC {
			return build_attr.ELECTRICITY
		}
		if cond.Id == item_id.WATER {
			return build_attr.WATER
		}
		if cond.Id == item_id.Vitality {
			return build_attr.VITALITY
		}
	}
	return build_attr.NONE
}

// Offline 角色离线
func (plr *Player) Offline() {
	if plr.Session != nil {
		plr.Session.UnBind()
		plr.Session.Push()
		plr.Session.Close()
		plr.Session = nil
	}
}

// IsOnline 获取玩家是否在线
func (plr *Player) IsOnline() bool {
	return plr.Session != nil
}

// IsValid 判断数据还是否可用
func (plr *Player) IsValid() bool {
	return !plr.Destroy
}

func (plr *Player) ToSimpleData() *pb.SimplePlayerData {
	data := db.GetRedis().HGetAll(context.TODO(), db.RKPlayerBaseInfo(plr.Id)).Val()
	return &pb.SimplePlayerData{
		Id:   plr.Id,
		Name: data["nickName"],
		Head: data["avatarUrl"],
	}
}

func (plr *Player) ToPvpSimpleData() *pb.PvpSimplePlayerData {
	data, rank := plr.GetNormalPvpData()
	if data == nil {
		return nil
	}
	return &pb.PvpSimplePlayerData{
		Ext:         plr.ToSimpleData(),
		Rank:        int32(rank),
		Score:       int32(data.Score),
		BattleRoles: lo.Map(data.Roles, func(item *BattleRole, index int) *pb.BattleRole { return item.ToPb() }),
	}
}

// ToPlayerBaseInfo 填充为一个pb对象
func (plr *Player) ToPlayerBaseInfo() *pb.Player {
	skinData := make(map[int32]*pb.PassengerSkinData)
	for k, v := range plr.Skins {
		data := &pb.PassengerSkinData{}
		for _, skin := range v {
			data.List = append(data.List, skin.ToPb())
		}
		skinData[int32(k)] = data
	}

	fragData := make(map[int32]int32)
	for k, v := range plr.FragItems {
		bean, _ := cfg.CharacterFragContainer.GetBeanById(cast.ToInt(k))
		if bean == nil {
			continue
		}
		fragData[int32(k)] = int32(v)
	}

	pp := &pb.Player{
		Uid:                 plr.Id,
		NickName:            plr.NickName,
		AvatarUrl:           plr.AvatarUrl,
		CreateTime:          cast.ToUint64(plr.CreateTime),
		OfflineTime:         cast.ToUint64(plr.LastLoginTime - plr.LastLogoutTime),
		Diamond:             cast.ToInt32(plr.Diamond),
		Heart:               cast.ToInt32(plr.Heart),
		StarDust:            cast.ToInt32(plr.StarDust),
		Gm:                  plr.GM,
		Energy:              plr.EnergyToPb(),
		Time:                cast.ToUint64(plr.GetWorldTime()),
		Bag:                 plr.ToItemInfoArr(),
		Train:               plr.Train.ToTrainInfo(),
		Passengers:          plr.ToPassengerInfoArr(),
		PlanetInfo:          plr.PlanetData.ToPb(),
		GuideId:             cast.ToInt32(plr.GuideId),
		GuideInfo:           plr.ToPbGuideInfo(),
		ToolModel:           plr.Tool.ToPb(),
		TaskInfo:            plr.Task.toPb(),
		Explore:             plr.Explore.ToPb(),
		NextDaySurpluTime:   int32(plr.GetNextDaySurplusTime()),
		TotalOnlineTime:     uint64(plr.TotalOnlineTime),
		AchievementInfo:     plr.Achievement.ToPb(),
		ChangeNameCnt:       cast.ToInt32(plr.ChangeNameCnt),
		NewMarkList:         plr.ToPbNewMarkList(),
		Chest:               plr.Chest.ToPb(),
		HeartOutput:         cast.ToInt32(plr.GetHeartOutput()),
		PassengerStarOutput: cast.ToInt32(plr.GetPassengerStarOutput()),
		Tower:               plr.Tower.ToPb(),
		BlackHole:           plr.BlackHole.ToPb(),
		Battle:              plr.Battle.ToPb(),
		Equip:               plr.Equip.ToPb(),
		Instance:            plr.Instance.ToPb(),
		PassengerRestCdTime: cast.ToInt32(ut.Max(0, plr.PassengerRestCdTime-plr.GetNowTime())),
		Store:               plr.Store.ToPb(),
		Skin:                skinData,
		Jackpot:             plr.JackpotMod.ToPb(),
		Pay:                 plr.Pay.ToPb(),
		Transport:           plr.Transport.ToPb(),
		Field:               plr.FieldModule.ToPb(),
		Frag:                fragData,
		Ore:                 plr.Ore.ToPb(),
		Collect:             plr.Collect.ToPb(),
		Arrest:              plr.ArrestModule.ToPb(),
		SpaceStone:          plr.SpaceStone.ToPb(),
		DailyTask:           plr.DailyTask.ToPb(),
		NextWeekSurplusTime: int32(plr.GetNextWeekSurplusTime()),
		PassengerProfiles:   lo.Map(plr.PassengerProfiles, func(item int, index int) int32 { return int32(item) }),
		PlanetProfiles:      lo.Map(plr.PlanetProfiles, func(item int, index int) int32 { return int32(item) }),
		Resonance:           plr.Resonance.ToPb(),
		OfflineRewardTime:   cast.ToInt32(plr.OutputRewardDuration),
		OffsetTime:          cast.ToInt32(plr.OffsetTime),
		PvpModuleData:       plr.Pvp.ToPb(),
		Ad:                  plr.Ad.ToPb(),
		ProfileBranch:       plr.ProfileBranch.ToPb(),
		TrainDailyTask:      plr.TrainDailyTask.ToPb(),
		BurstTask:           plr.BurstTask.ToPb(),
		TrainActivity:       plr.TrainActivity.ToPb(),
		TechData:            plr.Tech.ToPb(),
		TimeStone:           plr.TimeStone.ToPb(),
	}

	if comm.IsDebug() {
		pp.ConfigMd5 = script.ConfigMd5
	}
	return pp
}

// ToItemInfoArr 背包数据转pb前端数据
func (plr *Player) ToItemInfoArr() []*pb.ItemInfo {
	arr := make([]*pb.ItemInfo, 0)
	for _, item := range plr.Bag {
		arr = append(arr, item.ToItemInfo())
	}
	return arr
}

// ToPassengerInfoArr 乘客数据转pb前端数据
func (plr *Player) ToPassengerInfoArr() []*pb.PassengerInfo {
	arr := make([]*pb.PassengerInfo, 0)
	for _, passenger := range plr.Passenger {
		arr = append(arr, passenger.ToPb())
	}
	return arr
}

// TryGetPlayerFromDb 根据用户id从db中加载一个玩家出来，如果create=true将在加载失败后创建一个新player返回
func TryGetPlayerFromDb(id string, create bool) (plr *Player, err error) {
	if ut.IsEmpty(id) {
		return nil, errors.New("加载角色失败，必须指定所属账号id")
	}
	plr = &Player{}
	res := db.GetCollection(db.PLAYER).FindOne(context.TODO(), &bson.M{
		"id": id,
	})
	var result bson.M
	err = res.Decode(&result)
	if err != nil && err != mongo.ErrNoDocuments {
		log.Error("TryGetPlayerFromDb Error %s %s", id, err.Error())
		return nil, err
	}
	err = res.Decode(plr)
	if err != nil && err != mongo.ErrNoDocuments {
		log.Error("TryGetPlayerFromDb Error %s %s", id, err.Error())
		return nil, err
	}

	if err == mongo.ErrNoDocuments {
		if create {
			plr, err = NewPlayer(id)
			return
		} else {
			return nil, err
		}
	}
	// 补全uid信息
	if plr != nil && !plr.IsNew {
		oid := result["_id"].(primitive.ObjectID)
		plr.Uid, _ = ut.NewMongoIdFrom(oid.Hex())
	}
	return
}

func NewByBackup(id string, backupMid string) (plr *Player, err error) {
	if ut.IsEmpty(id) {
		return nil, errors.New("创建角色失败，必须指定所属账号id")
	}
	now := ut.Now()
	plr = &Player{}
	hex, err := primitive.ObjectIDFromHex(backupMid)
	if err != nil {
		return nil, err
	}
	singleResult := db.DELETE_PLAYER.GetCollection().FindOne(context.TODO(), bson.M{"_id": hex})
	var result bson.M
	_ = singleResult.Decode(&result)
	line := &struct{ Data string }{}
	err = singleResult.Decode(line)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal([]byte(line.Data), &plr)
	if err != nil {
		return nil, err
	}
	plr.IsNew = true
	plr.CreateTime = now
	plr.Id = id

	// 插入数据库
	res, err2 := db.PLAYER.GetCollection().InsertOne(context.TODO(), plr)
	if err2 != nil {
		if !mongo.IsDuplicateKeyError(err2) {
			log.Error("NewPlayer Error:%s", err2.Error())
		}
		return nil, err2
	}
	objId := res.InsertedID.(primitive.ObjectID)
	plr.Uid, err = ut.NewMongoIdFrom(objId.Hex())
	return
}

// NewPlayer 创建一个角色，需要指定所属账号id
func NewPlayer(id string) (plr *Player, err error) {
	if ut.IsEmpty(id) {
		return nil, errors.New("创建角色失败，必须指定所属账号id")
	}

	usr := GetUserFromDbById(id)

	now := ut.Now()
	sid := getServerAreaId()
	if sid <= 0 {
		return nil, errors.New("创建角色失败，获取区服id失败")
	}
	plr = &Player{
		Id:             id,
		CreateTime:     now,
		LastLoginTime:  now,
		GM:             comm.IsDebug(),
		IsNew:          true,
		Passenger:      make([]*Passenger, 0),
		Task:           NewPlayerTaskData(),
		PlanetData:     NewDefaultPlanetData(),
		GuideList:      make([]*GuideInfo, 0),
		LastUpdateTime: now,
		Bag:            make([]*Item, 0),
		ServerId:       sid,
		NickName:       usr.NickName,
		// AvatarUrl:      usr.AvatarUrl,
	}
	// 插入数据库
	result, err := db.PLAYER.GetCollection().InsertOne(context.TODO(), plr)
	if err != nil {
		if !mongo.IsDuplicateKeyError(err) {
			log.Error("NewPlayer Error:%s", err.Error())
		}
		return nil, err
	}
	// 缓存基础信息
	db.GetRedis().HSet(context.TODO(), db.RKPlayerBaseInfo(plr.Id),
		"nickName", plr.NickName,
		"avatarUrl", plr.AvatarUrl)
	objId := result.InsertedID.(primitive.ObjectID)
	plr.Uid, err = ut.NewMongoIdFrom(objId.Hex())
	return
}

func getServerAreaId() int {
	maxPlayers := 100000
	result, err := db.GetRedis().Eval(context.TODO(),
		lua.AllocateAreaScript,
		[]string{db.RKServerArea(), db.RKServerAreaIdx()},
		maxPlayers,
	).Result()
	if err != nil {
		log.Error("getServerAreaId Error:%s", err.Error())
		return 0
	}
	// 解析结果
	resultArray := result.([]interface{})
	serverId := int(resultArray[0].(int64))
	playerCount := int(resultArray[1].(int64))
	isNew := int(resultArray[2].(int64)) == 1
	log.Debug("分配区服成功: 区服ID=%d, 当前人数=%d, 是否新区服=%v", serverId, playerCount, isNew)
	return serverId
}

func (plr *Player) ReadySave() {
	// 同步一些字段的值
	now := plr.GetNowTime()
	plr.UpdateWorldTime()
	plr.UpdateAllOutput()
	plr.TotalOnlineTime += now - plr.LastSaveTime
	plr.LastSaveTime = now
}

// Save 全字段save
func (plr *Player) Save() {
	plr.ReadySave()
	plr.Mail.Save()
	plr.SaveToDb(ut.FieldToBsonAuto(plr))
}

func (plr *Player) OnLeave() {
	plr.LastLogoutTime = plr.GetNowTime()
	plr.StopSpeedUp()
	plr.Save()
	plr.TaPropertySetAfterLeave()
}

func (plr *Player) GetDefine() *db.TableDef {
	return db.PLAYER
}

func (plr *Player) GetUnique() string {
	return plr.Id
}

func (plr *Player) BulkSaveOperation() *mongo.UpdateOneModel {
	bsonAuto := ut.FieldToBsonAuto(plr)
	filter := &bson.M{"_id": plr.Uid.ObjectId()}
	update := &bson.M{"$set": &bsonAuto}
	operation := mongo.NewUpdateOneModel()
	operation.SetFilter(filter)
	operation.SetUpdate(update)
	operation.SetUpsert(true)
	return operation
}

// SaveToDb 轻量级分字段save
func (plr *Player) SaveToDb(v bson.M) {
	if len(v) == 0 {
		log.Warning("player:%s - SaveToDb,but v is empty :%v", plr.GetUid(), v)
		return
	}
	sTime := time.Now()
	filter := bson.M{}
	if plr.Uid != nil {
		filter["_id"] = plr.Uid.ObjectId()
	} else {
		filter["id"] = plr.Id
	}
	_, err := db.PLAYER.GetCollection().UpdateOne(context.TODO(), &filter, &bson.M{
		"$set": &v,
	}, options.Update().SetUpsert(true))
	if err != nil {
		log.Error("player:%s - SaveToDb:%v", plr.GetUid(), err.Error())
	}
	log.Info("player:%s - SaveToDb ,during :%fs", plr.GetUid(), time.Since(sTime).Seconds())
}

func (plr *Player) GetCurrency(currencyType int) int {
	switch currencyType {
	case condition.DIAMOND:
		return plr.Diamond
	case condition.STAR_DUST:
		return plr.StarDust
	case condition.HEART:
		return plr.Heart
	}
	return 0
}
func (plr *Player) ESetCurrency(currencyType int, num int) {
	plr.setCurrency(currencyType, num)
}
func (plr *Player) setCurrency(currencyType int, num int) {
	switch currencyType {
	case condition.DIAMOND:
		plr.Diamond = num
	case condition.STAR_DUST:
		plr.StarDust = num
	case condition.HEART:
		plr.Heart = num
	}
}

// changeCurrency 修改玩家货币 num < 0代表扣除
func (plr *Player) changeCurrency(currencyType int, num int) int {
	cur := plr.GetCurrency(currencyType)
	// plr.eventCenter.Emit(event.BeforeUpdateCurrency, currencyType, num)
	newVal := cur + num
	plr.setCurrency(currencyType, newVal)
	plr.addAccCurrency(currencyType, num)
	log.Info("[%s] 玩家货币:%d, 数量:%d -> %d", plr.GetUid(), currencyType, cur, newVal)
	plr.eventCenter.Emit(event.UpdateCurrency, currencyType, num)
	return num
}

func (plr *Player) addAccCurrency(currencyType int, num int) {
	if num <= 0 {
		return
	}
	switch currencyType {
	case condition.DIAMOND:
		plr.AccTotalDiamond += num
	case condition.STAR_DUST:
		plr.AccTotalStarDust += num
	case condition.HEART:
		plr.AccTotalHeart += num
	}
}

func (plr *Player) StartSpeedUp() {
	plr.UpdateSpeedUp()
	plr.Energy.LastSpeedUpTime = plr.GetNowTime()
}

func (plr *Player) StopSpeedUp() {
	plr.UpdateSpeedUp()
	plr.Energy.LastSpeedUpTime = 0
}

func (plr *Player) UpdateSpeedUp() {
	if !plr.Energy.IsSpeedUp() {
		return
	}
	passTime := plr.GetNowTime() - plr.Energy.LastSpeedUpTime
	if passTime > plr.Energy.Energy {
		passTime = plr.Energy.Energy
	}
	//更新能量
	plr.Energy.Energy -= passTime
	plr.SpeedUp(passTime)
	if plr.Energy.Energy <= 0 {
		plr.Energy.LastSpeedUpTime = 0
	} else {
		plr.Energy.LastSpeedUpTime += passTime
	}
}

// 加速x毫秒
func (plr *Player) SpeedUp(passTime int) {
	miscC := cfg.Misc_CContainer.GetObj()
	//更新模拟时间
	speedUpTime := TransToWorldTime(passTime) * (miscC.SpeedUp.S3 - 1)
	plr.SpeedUpTime += speedUpTime

	log.Info("[%s] 加速%d毫秒, 模拟时间增加%d毫秒", plr.GetUid(), passTime, speedUpTime)

	virtualTime := passTime * (miscC.SpeedUp.S8 - 1)

	// 加速车厢建造
	for _, carriage := range plr.Train.Carriages {
		if !carriage.IsBuilt() {
			carriage.SetBuiltSpeedUp(virtualTime)
		}
	}

	if plr.PlanetData.IsMove() {
		plr.PlanetData.addToSailingAddTime(virtualTime)
	}

	during := plr.AddOutputDuration(passTime * (miscC.SpeedUp.S3 - 1))

	//更新车厢产出
	for _, carriage := range plr.Train.Carriages {
		plr.UpdateCarriageOutputBySpeedUp(carriage, during)
	}
	// 更新宣传局产出
	for _, planet := range plr.PlanetData.Planets {
		planet.UpdatePublicityOutputByPassTime(during)
	}
}

func (plr *Player) UpdateAllOutput() {
	during := plr.updateOutputRewardDuration()
	log.Debug("UpdateAllOutput dur %d", during)
	// 更新车厢产出
	for _, carriage := range plr.Train.Carriages {
		plr.UpdateCarriageOutput(carriage, during)
	}
	// 宣传局玩法产出
	for _, planet := range plr.PlanetData.Planets {
		planet.UpdatePublicityOutputByPassTime(during)
	}
}

func (plr *Player) ResetLastOutputTime() {
	now := plr.GetNowTime()
	for _, carriage := range plr.Train.Carriages {
		carriage.LastUpdateOutputTime = now
	}
}

// DoJackpotRecord 记录抽卡
func (plr *Player) DoJackpotRecord(msgId string, result []*Condition) (history []*Condition) {
	if ut.IsEmpty(msgId) {
		return nil
	}
	if history = plr.JackpotHasRecord(msgId); history != nil {
		return
	}
	// 只保留最新的10次记录
	if size := len(plr.GetJackpotHistory()); size > 10 {
		temp := make(map[string][]*Condition)
		idx := 0
		for i, ints := range plr.GetJackpotHistory() {
			if size-idx <= 10 {
				temp[i] = ints
			}
			idx++
		}
		plr.SetJackpotHistory(temp)
	}
	plr.GetJackpotHistory()[msgId] = result
	return result
}

func (plr *Player) JackpotHasRecord(msgId string) (history []*Condition) {
	if ut.IsEmpty(msgId) {
		return nil
	}
	if plr.GetJackpotHistory() == nil {
		plr.SetJackpotHistory(make(map[string][]*Condition))
	}
	if history = plr.GetJackpotHistory()[msgId]; history != nil {
		return
	}
	return nil
}

// TrackEntry 数数事件 ta-entry
func (plr *Player) TrackEntry(isReconnect bool) {
	ta.TrackBySession(plr.Session, ta.Entry, map[string]interface{}{
		"is_reconnect": isReconnect,
	})
}

// TaPropertySetAfterCreate 创角后上报用户属性
func (plr *Player) TaPropertySetAfterCreate() {
	ta.UserSet(plr.Session, map[string]interface{}{
		"uid":               plr.GetUid(),
		"uid_register_time": plr.CreateTime,
		"os_version":        plr.Session.Get(enum.OS),
		"platform":          plr.Session.Get(enum.Platform),
	})
}

// TaPropertySetAfterLeave 离线后上报用户属性
func (plr *Player) TaPropertySetAfterLeave() {
	now := time.Now()
	if now.Sub(plr.LastLeaveReportTime).Seconds() <= time.Minute.Seconds()*30 {
		return
	}
	ta.UserSet(plr.Session, map[string]interface{}{
		"version":          plr.GetClientVersion(),
		"stardust":         plr.GetCurrency(condition.STAR_DUST),
		"accTotalStardust": plr.AccTotalStarDust,
		"heart":            plr.GetCurrency(condition.HEART),
		"accTotalHeart":    plr.AccTotalHeart,
		"diamond":          plr.GetCurrency(condition.DIAMOND),
		"accTotalDiamond":  plr.AccTotalDiamond,
		"mainLevel":        plr.GetMaxMainLevelId(),
	})
	plr.LastLeaveReportTime = now
}

// TaTrackResChange 资源变化上报
func (plr *Player) TaTrackResChange(cond *Condition, change, sceneType int) {
	ta.TrackBySession(plr.Session, ta.ResChange, map[string]interface{}{
		"res_type":   cond.Type,
		"res_id":     cast.ToString(cond.Id),
		"res_num":    cond.Num * change,
		"scene_type": sceneType,
		"mainLevel":  plr.GetMaxMainLevelId(),
	})
}

func (plr *Player) UnlockFunction(id string) {
	switch id {
	case function_type.BLACK_HOLE:
		plr.BlackHole.Unlock()
	case function_type.TOWER:
		plr.Tower.Unlock()
	case function_type.DAILY_TASK:
		plr.DailyTask.Unlock()
	case function_type.ORE:
		plr.Ore.Unlock()
	case function_type.TRANSPORT:
		plr.Transport.Unlock()
	case function_type.INSTANCE:
		plr.Instance.Unlock()
	case function_type.FIELD:
		plr.FieldModule.Unlock()
	case function_type.PLAY_PVP_1:
		plr.Pvp.Unlock(pb.PvpType_NORMAL)
	case function_type.PLAY_PVP_2:
		plr.Pvp.Unlock(pb.PvpType_HIGH)
	case function_type.PLAY_PVP_3:
		plr.Pvp.Unlock(pb.PvpType_PEAK)
	case function_type.PLAY_TRAIN_DAILY_TASK:
		plr.TrainDailyTask.Unlock()
	case function_type.TRAIN_ACTIVITY:
		plr.TrainActivity.Unlock()
	}
}

func (plr *Player) DynamicTriggerCheck() {
	data := cfg.DynamicStepContainer.GetData()
	if plr.DynamicStepTriggered == nil {
		plr.DynamicStepTriggered = make([]int, 0)
	}
	if len(plr.DynamicStepTriggered) > 0 {
		data = lo.Filter(data, func(d *cfg.DynamicStep[int], i int) bool { return !lo.Contains(plr.DynamicStepTriggered, d.Id) })
	}
	for _, d := range data {
		switch d.Trigger {
		case dynamic_trigger_type.Pass_Planet_Node:
			argId := d.TriggerArg[0].(string)
			if plr.PlanetData.isPassNode(argId) {
				plr.dynamicTriggerDo(d.Id, d.Function, d.FuncArg)
			}
		}
	}
}
func (plr *Player) dynamicTriggerDo(sortId int, function int, arg []any) {
	log.Debug("dynamicTriggerDo %d %v", function, arg)
	switch function {
	case dynamic_trigger_function_type.BurstTask:
		item := plr.BurstTask.Unlock(cast.ToInt(arg[0]))
		if item != nil {
			plr.TellPlayerMsg(pb.S2COnGetBurstTaskMessage, &pb.S2C_OnGetBurstTaskMessage{Data: item.ToPb()})
		}

	}
	plr.DynamicStepTriggered = append(plr.DynamicStepTriggered, sortId)
}

func (plr *Player) TellPlayerMsg(topic string, msg protoreflect.ProtoMessage) bool {
	if plr.Session == nil {
		return false
	}
	err := plr.Session.SendNR(topic, pb.ProtoMarshalForce(msg))
	if err == "" {
		return true
	}
	log.Error("TellPlayerMsg 发送给客户端消息时出错:%s", err)
	return false
}

// SetChangeNameCount 修改名称的次数
func (plr *Player) SetChangeNameCount(count int) {
	plr.ChangeNameCnt = count
}
func (plr *Player) GetChangeNameCount() int {
	return plr.ChangeNameCnt
}

func (plr *Player) ChangeNickName(name string) {
	plr.NickName = name
	plr.SetChangeNameCount(plr.GetChangeNameCount() + 1)
}

func (plr *Player) onGlobalListener() {
	eventCenter := plr.eventCenter

	//监听触发功能解锁
	eventCenter.On(event.PlanetNodeComplete, func(val ...interface{}) {
		plr.CheckUnlockFunctions()
	})
	eventCenter.On(event.PlanetNodeComplete, func(val ...interface{}) {
		plr.DynamicTriggerCheck()
	})

	plr.eventCenter.On(event.PassengerLevelUp, func(i ...interface{}) {
		plr.RecordAvgLv()
	})
}

func (plr *Player) GetOutputSum(t int) float64 {
	carriages := plr.Train.Carriages
	sum := 0.0
	passeners := plr.Passenger
	if t == condition.STAR_DUST {
		for _, carriage := range carriages {
			sum += math.Floor(carriage.StarOutput.Val)
		}
		sum += plr.PassengerStarOutput
		for _, passenegr := range passeners {
			sum += math.Floor(passenegr.StarOutput)
		}
	} else if t == condition.HEART {
		for _, carriage := range carriages {
			sum += math.Floor(carriage.HeartOutput.Val)
		}
		for _, passenegr := range passeners {
			sum += math.Floor(passenegr.HeartOutput)
		}
	}
	// log.Debug("GetOutputSum %d %f", t, sum)
	return sum
}

func (plr *Player) GetNextDaySurplusTime() int {
	misc := cfg.Misc_CContainer.GetObj()
	refreshTime := misc.RefreshTime
	now := plr.GetNowTime()
	now -= ut.DateZeroTime(now)
	if now > refreshTime {
		refreshTime += ut.TIME_DAY
	}
	return refreshTime - now
}

// CheckRefreshDaily
/*
 * @description 检查每日凌晨刷新
 * @param lastTime
 * @param day
 * @return bool
 */
func (plr *Player) CheckRefreshDaily(lastTime int, day int) bool {
	return plr.GetToDaySurplusTime(lastTime, day) <= 0
}

// CheckRefreshDailyPm
/*
 * @description 检查每日下午刷新
 * @param lastTime
 * @param day
 * @return bool
 */
func (plr *Player) CheckRefreshDailyPm(lastTime int, day int) bool {
	misc := cfg.Misc_CContainer.GetObj()
	return ut.TimeUntilNextRefresh(lastTime, plr.GetNowTime(), misc.RefreshTimePm, day) <= 0
}

func (plr *Player) CheckRefreshWeek() bool {
	return plr.GetNextWeekSurplusTime() <= 0
}

// GetNextWeekSurplusTime 周刷新时间
func (plr *Player) GetNextWeekSurplusTime() int {
	misc := cfg.Misc_CContainer.GetObj()
	now := plr.GetNowTime()
	return ut.GetNextWeekRefreshTime(misc.RefreshTime, plr.LastWeekUpdateTime) - now
}

// 这个函数修改前先和yzm商量一下
func (plr *Player) GetToDaySurplusTime(lastTime int, day int) int {
	misc := cfg.Misc_CContainer.GetObj()
	return ut.TimeUntilNextRefresh(lastTime, plr.GetNowTime(), misc.RefreshTime, day)
	//now := plr.GetNowTime() + misc.RefreshTime
	//refreshTime := ut.DateZeroTime(lastTime + misc.RefreshTime) //下次刷新时间
	//refreshTime += ut.TIME_DAY * day
	//return refreshTime - now
}

// RandomPassenger
/*
 * @description 在给定的dataArray配置乘客中随机一个 如果没有就从已上机的乘客配置中随机
 * @param dataArray
 * @return Condition 返回的是一个Condition对象
 */
func (plr *Player) RandomPassenger(dataArray ...*cfg.Character[int]) *cfg.Character[int] {
	if dataArray == nil || len(dataArray) == 0 {
		dataArray = cfg.CharacterContainer.GetData()
	}
	var bean *cfg.Character[int]
	if len(dataArray) == 1 {
		bean = dataArray[0]
	} else {
		idx := ut.RandomIndexByWeight(dataArray, func(d *cfg.Character[int]) int { return d.Weight })
		bean = dataArray[idx]
	}
	return bean
}

// CheckChangePassenger 检测并转换乘客碎片 (投影)
func (plr *Player) CheckChangePassenger(curCond *cfg.Character[int], outConds []*Condition) *Condition {
	id := curCond.Id
	_, bol := lo.Find(outConds, func(m *Condition) bool { return m.Type == condition.PASSENGER && m.Id == id })
	if bol || plr.GetPassengerById(id) != nil {
		bean, _ := cfg.CharacterContainer.GetBeanById(id)
		characterFrag, _ := lo.Find(cfg.CharacterFragContainer.GetData(), func(frag *cfg.CharacterFrag[int]) bool { return frag.CharacterId == id && frag.Quality == bean.Quality })
		return &Condition{
			Type: condition.PASSENGER_FRAG,
			Id:   characterFrag.Id,
			Num:  1,
		}
	}
	return &Condition{
		Type: condition.PASSENGER,
		Id:   id,
	}
}

// 获取当前爱心产出
func (plr *Player) GetHeartOutput() float64 {
	sum := 0.0
	for _, carriage := range plr.Train.Carriages {
		sum += carriage.HeartOutput.Val
	}
	for _, passenegr := range plr.Passenger {
		sum += passenegr.HeartOutput
	}
	return sum
}

// 领取产出爱心
func (plr *Player) ClaimHeart(num int) int {
	sum := plr.GetHeartOutput()

	output := cast.ToInt(sum)
	if num > output || num <= 0 {
		num = output
	}

	numF := float64(num)
	for _, carriage := range plr.Train.Carriages {
		if numF <= 0 {
			break
		}
		originalVal := carriage.HeartOutput.Val
		deduct := math.Min(originalVal, numF)
		carriage.HeartOutput.Val = originalVal - deduct
		numF -= deduct
	}
	for _, passenegr := range plr.Passenger {
		if numF <= 0 {
			break
		}
		originalVal := passenegr.HeartOutput
		deduct := math.Min(originalVal, numF)
		passenegr.HeartOutput = originalVal - deduct
		numF -= deduct
	}

	plr.GrantReward(&Condition{Type: condition.HEART, Num: num}, ta.ResChangeSceneTypeTrainPickUp)
	log.Info("[%s] 领取爱心,本次领取数量:%d, 剩余:%f", plr.GetUid(), num, plr.GetHeartOutput())
	return num
}

func (plr *Player) initUnlockFunction() {
	plr.UnlockFuncs = array.Filter(cfg.UnlockFuncContainer.GetData(), func(data *cfg.UnlockFunc[int], i int) bool {
		return plr.IsUnlockFunction(data.Type)
	})
}

func (plr *Player) CheckUnlockFunctions() {
	unlockFuncs := array.Filter(cfg.UnlockFuncContainer.GetData(), func(data *cfg.UnlockFunc[int], i int) bool {
		if plr.HasUnlockFunction(data.Type) {
			return false
		}
		return plr.IsUnlockFunction(data.Type)
	})
	for _, fn := range unlockFuncs {
		plr.UnlockFunction(fn.Type)
		plr.UnlockFuncs = append(plr.UnlockFuncs, fn)
	}
}

// 已解锁过
func (plr *Player) HasUnlockFunction(Type string) bool {
	if plr.CloseUnlockFunc {
		return true
	}

	return array.Some(plr.UnlockFuncs, func(fn *cfg.UnlockFunc[int]) bool {
		return fn.Type == Type
	})
}

// 是否满足解锁条件
func (plr *Player) IsUnlockFunction(Type string) bool {
	unlockFunc := array.Find(cfg.UnlockFuncContainer.GetData(), func(data *cfg.UnlockFunc[int]) bool {
		return data.Type == Type
	})

	if unlockFunc == nil || unlockFunc.IsShow == 0 {
		if plr.CloseGuide {
			return true
		}
	}

	if Type == function_type.INSTANCE {
		return plr.Instance.IsUnlock
	} else if Type == function_type.ORE {
		return plr.Ore.IsUnlock
	} else if Type == function_type.BLACK_HOLE {
		return plr.BlackHole.IsUnlock
	}

	if unlockFunc == nil {
		return true
	}
	targets := unlockFunc.Target
	if len(targets) <= 0 {
		return true
	}

	check := func(target *cfg.TaskTarget) bool {
		iType := cast.ToInt(target.Type)
		if iType == condition.BUILD_ID {
			id := cast.ToString(target.Id)
			bean, _ := cfg.TrainItemContainer.GetBeanByUnique(id)
			carriage := plr.GetCarriageById(bean.CarriageId)
			if carriage == nil {
				return false
			}
			build := carriage.GetBuildById(id)
			return build != nil
		} else if iType == condition.PLANET_NODE {
			return plr.PlanetData.isPassNode(cast.ToString(target.Id))
		} else if iType == condition.PLANET_BATTLE_NODE {
			return plr.PlanetData.isPassBattle(cast.ToString(target.Id))
		} else if iType == condition.PLANET {
			planet := plr.PlanetData.GetPlanet(cast.ToInt(target.Id))
			if planet == nil {
				return false
			}
			return planet.IsDone()
		}
		return true
	}

	return lo.EveryBy(targets, check)
}

// IsCanRestPassenger
/*
 * @description 重置乘客cd到了没有
 * @return bool
 */
func (plr *Player) IsCanRestPassenger() bool {
	return plr.GetPassengerRestCd() <= 0
}

func (plr *Player) GetPassengerRestCd() int {
	return plr.PassengerRestCdTime - plr.GetNowTime()
}

func GetNoMonsters(tot int, index int, lv int) []int {
	monsterDatas := cfg.PlanetMonsterContainer.GetData()
	entrustDatas := cfg.MonsterCampContainer.GetData()
	groupLv := entrustDatas[len(entrustDatas)-1].LevelMax
	for _, data := range entrustDatas {
		if data.LevelMax >= lv {
			groupLv = ut.Min(groupLv, data.LevelMax)
		}
	}
	entrustDatas = lo.Filter(entrustDatas, func(data *cfg.MonsterCamp[string], i int) bool {
		return groupLv == data.LevelMax && (index+1 == data.Array || (index-tot) == data.Array)
	})
	noMonsters := []int{}
	for _, data := range monsterDatas {
		if data.IsBoss == 1 || data.IsSp == 1 {
			noMonsters = append(noMonsters, data.ID)
		}
	}
	for _, data := range entrustDatas {
		for _, id := range data.NoMonster {
			if !array.Has(noMonsters, id) {
				noMonsters = append(noMonsters, id)
			}
		}
	}
	return noMonsters
}

// randomMonster
/*
 * @description
 * @param tot 总共随机多少个怪物
 * @param index 怪物所在站位位置
 * @param lv 怪物的等级
 */
func (plr *Player) randomMonster(tot, index, lv int, monsters []*BattleRole) *BattleRole {
	monsterDatas := cfg.PlanetMonsterContainer.GetData()
	noMonsters := GetNoMonsters(tot, index, lv)
	monsterDatas = lo.Filter(monsterDatas, func(data *cfg.PlanetMonster[int], i int) bool {
		if array.Some(monsters, func(m *BattleRole) bool { return m != nil && m.Id == data.ID }) {
			return false
		}
		if array.Has(noMonsters, data.ID) {
			return false
		}
		return true
	})

	rdIndex := ut.Random(0, len(monsterDatas)-1)
	monster := monsterDatas[rdIndex]
	return &BattleRole{
		Id:     monster.ID,
		StarLv: cfg.GetMonsterStarByLevel(lv),
		Lv:     lv,
	}
}

func (plr *Player) RecordAvgLv() {
	lvAvg := plr.GetPassengerAvgLv(5)
	plr.AvgLv = ut.Max(lvAvg, plr.AvgLv)
}

func (plr *Player) GetAvgLv() int {
	if plr.AvgLv <= 0 {
		plr.RecordAvgLv()
	}
	return plr.AvgLv
}

func (plr *Player) RecordPassNode(nodeId string) {
	// preData := planet.GetMapDataById(planet.CurNodeId)
	if !comm.IsBackup() {
		return
	}

	buf := bufferPool.Get().(*bytes.Buffer)
	buf.Reset()
	defer bufferPool.Put(buf)

	gz := gzipPool.Get().(*gzip.Writer)
	gz.Reset(buf)

	defer func() {
		gz.Close() // 确保关闭
		gzipPool.Put(gz)
	}()

	// 直接写入 gz，跳过中间 bytes 变量
	enc := json.NewEncoder(gz)
	// 禁用 HTML 转义以减少输出大小
	enc.SetEscapeHTML(false)
	// 禁用缩进
	enc.SetIndent("", "")

	if err := enc.Encode(ut.FieldToBsonAuto(plr)); err != nil {
		log.Error("序列化数据失败: %v", err)
		return
	}
	if err := gz.Close(); err != nil {
		log.Error("关闭压缩器失败: %v", err)
		return
	}

	record := bson.M{
		"id":        plr.Id,
		"timestamp": time.Now().UnixMilli(),
		"data":      primitive.Binary{Data: buf.Bytes()},
		"extra":     nodeId,
	}
	_, err := db.FIX_RECORD.GetCollection().InsertOne(context.TODO(), &record)
	if err != nil {
		log.Error("记录数据失败: %v", err)
	}
}

func (plr *Player) AddAccCount(cond *Condition) {
	key := fmt.Sprintf("%d-%s", cond.Type, cast.ToString(cond.Id))
	plr.AccCount[key] += cond.Num
}

func (plr *Player) GetAccCount(cond *Condition) int {
	key := fmt.Sprintf("%d-%s", cond.Type, cast.ToString(cond.Id))
	return plr.AccCount[key]
}

// GenerateRewards
/*
 * @description 生成奖励列表
 * @param rewards 必得奖励
 * @param rdRewards 随机奖励(一份)
 * @return []*Condition
 */
func (plr *Player) GenerateRewards(rewards []*cfg.ChestReward, args ...interface{}) []*Condition {
	r1 := make([]*Condition, 0)
	r2 := make([]*Condition, 0)

	var rdRewards []*cfg.ChestReward
	var opt *cfg.GenerateRewardsOpt

	if len(args) > 0 {
		if v, ok := args[0].([]*cfg.ChestReward); ok {
			rdRewards = v
		}
	}
	if len(args) > 1 {
		if v, ok := args[1].(*cfg.GenerateRewardsOpt); ok {
			opt = v
		}
	}

	if rewards != nil {
		r1 = lo.Map(rewards, func(m *cfg.ChestReward, i int) *Condition {
			return plr.ChestRewardToCondition(m)
		})
	}

	if rdRewards != nil {
		randomCnt := 1
		if opt != nil {
			if opt.RandomCnt != nil {
				randomCnt = ut.Random(opt.RandomCnt.Min, opt.RandomCnt.Max)
			}
		}
		for i := 0; i < randomCnt; i++ {
			idx := ut.RandomIndexByWeight(rdRewards, func(r *cfg.ChestReward) int { return r.Weight })
			if idx >= 0 {
				r2 = append(r2, plr.ChestRewardToCondition(rdRewards[idx]))
			}
		}
	}

	return MergeConditions(r1, r2)
}

func (plr *Player) AddPlanetProfile(id int) {
	plr.PlanetProfiles = append(plr.PlanetProfiles, id)
}

func (plr *Player) RemovePlanetProfile(id int) {
	plr.PlanetProfiles = array.Remove(plr.PlanetProfiles, id)
}
