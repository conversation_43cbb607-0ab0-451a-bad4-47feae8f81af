package structs

import (
	"context"
	"time"
	"train/base/cfg"
	"train/common/pb"
	"train/common/ta"
	"train/db"
	ut "train/utils"
	wechatpay "train/utils/wechat_pay"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	PRODUCT_TYPE_DIAMOND = iota + 1
	PRODUCT_TYPE_PRIVILEGE_CARD
)

// 订单等待支付超时时间
var timeout = time.Minute * 60

func NewPayModule() *PayModule {
	return &PayModule{
		NotFinishOrders: make([]*PayOrder, 0),
		PayCountMap:     make(map[string]int),
	}
}

type PayModule struct {
	NotFinishOrders []*PayOrder    `bson:"notFinishOrders"` // 未完成订单列表
	PayCountMap     map[string]int `bson:"payCountMap"`     // 订单购买次数
	Card            map[string]int `bson:"card"`            // 特权卡数据
	plr             *Player        `bson:"-"`               // 玩家
}

func (p *PayModule) init(plr *Player) {
	p.plr = plr
	key_timeout := "timeout"
	key_finish := "finish"
	key_refund := "refund"
	key_reserve := "reserve"
	dataMap := lo.GroupBy(p.NotFinishOrders, func(item *PayOrder) string {
		// 未支付 并且超时了 移动到订单表去
		if item.State == pb.OrderState_NOT_PAY && time.Now().Unix() > int64(item.CreateTime)+int64(timeout.Seconds()) {
			item.State = pb.OrderState_FAIL_TIMEOUT
			return key_timeout
		}
		// 已经完成  移动到订单表去
		if item.State == pb.OrderState_FINISH {
			return key_finish
		}
		// 退款的
		if item.State == pb.OrderState_REFUND {
			return key_refund
		}
		return key_reserve
	})

	// 保留
	p.NotFinishOrders = dataMap[key_reserve]
	archiveOrders := make([]*PayOrder, 0)
	archiveOrders = append(archiveOrders, dataMap[key_timeout]...)
	archiveOrders = append(archiveOrders, dataMap[key_finish]...)
	archiveOrders = append(archiveOrders, dataMap[key_refund]...)
	if len(archiveOrders) > 0 {
		// 批量保存到数据库
		BatchSaveOrder(archiveOrders...)
	}
}

// 是不是首购
func (p *PayModule) IsFirstPay(productId string) bool {
	count := p.PayCountMap[productId]
	return count == 0
}

func (p *PayModule) ToPb() *pb.Pay {
	return &pb.Pay{
		PayCountMap: lo.MapEntries(p.PayCountMap, func(key string, val int) (string, int32) { return key, int32(val) }),
	}
}

func (p *PayModule) CheckOrder(payOrder *PayOrder) {
	if payOrder == nil {
		return
	}
	// 已经完成的不管
	if payOrder.State == pb.OrderState_FINISH {
		return
	}
	// 未支付
	if payOrder.State == pb.OrderState_NOT_PAY {
		switch payOrder.Platform {
		case pb.OrderPayPlatform_WechatPay:
			success, payTime := wechatpay.QueryOrder(payOrder.OrderId, payOrder.Amount)
			if success {
				payOrder.State = pb.OrderState_PAY
				payOrder.PurchaseTime = payTime
			}
		}
	}
	// 尝试处理已支付
	p.FinishOrder(payOrder)
}

func (p *PayModule) FinishOrder(payOrder *PayOrder) {
	if payOrder.State != pb.OrderState_PAY {
		return
	}
	productId := payOrder.ProductId
	shopBean := cfg.ShopContainer.GetBean(productId)
	if shopBean == nil {
		return
	}
	plr := p.plr
	first := p.IsFirstPay(productId)
	rewards := make([]*Condition, 0)
	if shopBean.Product != nil {
		products := lo.Map(shopBean.Product, func(item *cfg.ChestReward, index int) *Condition {
			return plr.ChestRewardToCondition(item)
		})
		rewards = append(rewards, products...)
	}
	if first && shopBean.First != nil {
		firsts := lo.Map(shopBean.First, func(item *cfg.ChestReward, index int) *Condition {
			return plr.ChestRewardToCondition(item)
		})
		rewards = append(rewards, firsts...)
	}
	if shopBean.Gifts != nil {
		gifts := lo.Map(shopBean.Gifts, func(item *cfg.ChestReward, index int) *Condition {
			return plr.ChestRewardToCondition(item)
		})
		rewards = append(rewards, gifts...)
	}

	switch shopBean.Type {
	case PRODUCT_TYPE_PRIVILEGE_CARD:
		cur := p.Card[productId]
		p.Card[productId] = cur + shopBean.Day
	}

	p.PayCountMap[productId]++
	plr.GrantRewards(rewards, ta.ResChangeSceneTypePay)
	payOrder.State = pb.OrderState_FINISH
	payOrder.FinishTime = ut.Now()
}

// FindOrderById 根据订单ID查找订单
func (p *PayModule) FindOrderById(orderId string) *PayOrder {
	order, found := lo.Find(p.NotFinishOrders, func(order *PayOrder) bool {
		return order.OrderId == orderId
	})
	if found {
		return order
	}
	return nil
}

// UpdateOrderStatus 更新订单状态
func (p *PayModule) UpdateOrderStatus(orderId string, state pb.OrderState, purchaseTime int, thirdOrderId string) bool {
	order := p.FindOrderById(orderId)
	if order == nil {
		log.Error("订单不存在: %s", orderId)
		return false
	}

	order.State = state
	if purchaseTime > 0 {
		order.PurchaseTime = purchaseTime
	}
	if thirdOrderId != "" {
		order.ThirdOrderId = thirdOrderId
	}

	// 如果是支付成功，尝试完成订单
	if state == pb.OrderState_PAY {
		p.FinishOrder(order)
	}

	log.Info("订单状态更新成功: %s, 状态: %v", orderId, state)
	return true
}

func (p *PayModule) CreateOrder(productId string, platform pb.OrderPayPlatform) (int32, map[string]string) {
	shopBean := cfg.ShopContainer.GetBean(productId)
	if shopBean == nil {
		return 1, nil
	}

	options := make(map[string]string)
	orderId := cast.ToString(ut.GenId())
	payOrder := &PayOrder{
		OrderId:    orderId,
		UserId:     p.plr.Id,
		State:      pb.OrderState_NOT_PAY,
		ProductId:  productId,
		Platform:   platform,
		CreateTime: ut.Now(),
		Quantity:   1,
	}
	options["orderId"] = orderId
	first := p.IsFirstPay(productId)
	price := shopBean.Rmb
	if first {
		price = shopBean.FirstRmb
	}
	payOrder.Amount = int64(price * 100)
	switch platform {
	case pb.OrderPayPlatform_WechatPay:
		prepay_id := wechatpay.CreateOrder(shopBean.SimpleDesc, orderId, timeout, payOrder.Amount)
		if prepay_id == "" {
			return 2, nil
		}
		payOrder.CurrencyType = "CNY"
		options["prepay_id"] = prepay_id
	}
	p.NotFinishOrders = append(p.NotFinishOrders, payOrder)
	return 0, options
}

// BatchSaveOrder 批量保存订单到数据库
func BatchSaveOrder(orders ...*PayOrder) {
	if len(orders) == 0 {
		return
	}

	col := db.PAY.GetCollection()
	ctx := context.TODO()

	// 准备批量写入操作
	var operations []mongo.WriteModel

	for _, order := range orders {
		if order == nil {
			continue
		}
		// 如果订单已存在则更新，不存在则插入
		operation := mongo.NewReplaceOneModel()
		operation.SetFilter(map[string]interface{}{
			"orderId": order.OrderId,
		})
		operation.SetReplacement(order)
		operation.SetUpsert(true)

		operations = append(operations, operation)
	}

	if len(operations) == 0 {
		return
	}

	// 执行批量写入
	opts := options.BulkWrite()
	// 无序执行，提高性能
	opts.SetOrdered(false)

	result, err := col.BulkWrite(ctx, operations, opts)
	if err != nil {
		log.Error("BatchSaveOrder failed: %v", err)
		return
	}
	log.Info("BatchSaveOrder success: inserted=%d, modified=%d, upserted=%d",
		result.InsertedCount, result.ModifiedCount, result.UpsertedCount)
}
