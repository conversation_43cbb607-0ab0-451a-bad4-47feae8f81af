package structs

import (
	"train/base/cfg"
	"train/common/pb"
	ut "train/utils"

	"github.com/samber/lo"
)

type EnergyModel struct {
	FreeRecoverNum      int          `bson:"freeRecoverNum"` //剩余免费恢复次数
	Energy              int          `bson:"energy"`         //存的是毫秒数
	json                *cfg.SpeedUp `bson:"-"`              //配置
	plr                 *Player      `bson:"-"`
	IsUnlockSpeedUpAuto bool         `bson:"isUnlockSpeedUpAuto"` //是否解锁自动加速
}

func NewEnergy() *EnergyModel {
	model := &EnergyModel{Energy: -1}
	return model
}

func (e *EnergyModel) GetJson() *cfg.SpeedUp {
	return e.json
}

func (e *EnergyModel) init() {
	if e.Energy < 0 {
		e.Reset()
	}
}

func (e *EnergyModel) ToPb() *pb.Energy {
	return &pb.Energy{
		Energy:              int32(e.Energy),
		FreeRecoverNum:      int32(e.FreeRecoverNum),
		IsUnlockSpeedUpAuto: e.IsUnlockSpeedUpAuto,
		NextRecoverTime:     int32(e.NextRecoverTime()),
	}
}

// RecoverEnergy
/*
 * @description : 恢复能量
 */
func (e *EnergyModel) RecoverEnergy() {
	cfg := e.GetJson()
	base := lo.If(e.IsUnlockSpeedUpAuto, cfg.AutoMax).Else(cfg.Max)
	e.Energy = base * ut.TIME_SECOND
}

// 重置
func (e *EnergyModel) Reset() {
	// 能量回满
	e.RecoverEnergy()
	// 次数回满
	e.FreeRecoverNum = e.GetJson().RecoverFree
}

// 下次触发恢复的时间
func (e *EnergyModel) NextRecoverTime() int {
	plr := e.plr
	misc := cfg.Misc_CContainer.GetObj()
	am := ut.TimeUntilNextRefresh(plr.LastUpdateTime, plr.GetNowTime(), misc.RefreshTime, 1)
	pm := ut.TimeUntilNextRefresh(plr.LastUpdateTime, plr.GetNowTime(), misc.RefreshTimePm, 1)
	return ut.Min(am, pm)
}

type AutoRecoverEnergy struct {
	*EnergyModel    `bson:",inline"`
	LastSpeedUpTime int `bson:"lastSpeedUpTime"` //上次加速时间
}

func NewAutoRecoverEnergy() *AutoRecoverEnergy {
	model := &AutoRecoverEnergy{EnergyModel: NewEnergy()}
	return model
}

func (a *AutoRecoverEnergy) Init(plr *Player) {
	if a.EnergyModel == nil {
		a.EnergyModel = NewEnergy()
	}
	a.json = cfg.Misc_CContainer.GetObj().SpeedUp
	a.plr = plr
	a.init()
}

func (a *AutoRecoverEnergy) IsSpeedUp() bool {
	return a.LastSpeedUpTime > 0
}

func (plr *Player) EnergyToPb() *pb.Energy {
	plr.UpdateSpeedUp()
	pb := plr.Energy.ToPb()
	pb.Used = plr.SpeedUpTime > 0
	pb.IsSpeedUp = plr.Energy.IsSpeedUp()
	return pb
}
