package structs

import (
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/data/item_id"
	"train/common/pb"
	"train/common/ta"
	ut "train/utils"

	"github.com/samber/lo"
	"github.com/spf13/cast"
)

const MaxRecords = 10

func NewTimeStone() *TimeStone {
	return &TimeStone{}
}

type TimeStone struct {
	TotalUseCnt int                      `bson:"totalUseCnt"` // 累计使用次数
	Lv          int                      `bson:"lv"`          // 等级
	Energy      int                      `bson:"energy"`      // 剩余能量
	Events      []*TimeStoneEventWrapper `bson:"events"`      // 时间记录
	plr         *Player                  `bson:"-"`
}

func (t *TimeStone) init(plr *Player) {
	t.plr = plr
}

func (t *TimeStone) ToPb() *pb.TimeStone {
	return &pb.TimeStone{
		Lv:     cast.ToInt32(t.Lv),
		Energy: cast.ToInt32(t.Energy),
		Events: lo.Map(t.Events, func(item *TimeStoneEventWrapper, index int) *pb.TimeStoneEventWrapper { return item.ToPb() }),
	}
}

func (t *TimeStone) GetJson() *cfg.TimeStone[int] {
	bean, _ := cfg.TimeStoneContainer.GetBeanById(t.Lv)
	return bean
}

func (t *TimeStone) GetMaxEnergy() int {
	if t.Lv == 0 {
		return 0
	}
	return t.GetJson().Energy
}

func (t *TimeStone) Refresh() {
	if t.Lv > 0 && t.Energy < t.GetMaxEnergy() {
		t.Energy = ut.Min(t.Energy+t.GetJson().DailyRecover, t.GetMaxEnergy())
	}
}

func (t *TimeStone) LvUp() {
	preMaxEnergy := t.GetMaxEnergy()

	t.Lv++
	maxEnergy := t.GetMaxEnergy()
	t.Energy += maxEnergy - preMaxEnergy

	if t.Lv == 1 {
		cond := &Condition{Type: condition.PROP, Id: item_id.TIME_STONE, Num: 1}
		if !t.plr.CheckCondition(cond) {
			t.plr.GrantReward(cond, ta.ResChangeSceneTypeUnknown)
		}
		t.Refresh()
	}
}

func (t *TimeStone) checkMax() {
	if t.Events == nil {
		t.Events = make([]*TimeStoneEventWrapper, 0)
		return
	}
	t.Events = t.Events[:t.GetJson().EventMax]
}

// 添加事件记录
func (t *TimeStone) PushEventBy(ary ...TimeStoneEvent) {
	if t.Lv == 0 {
		return
	}
	if t.Events == nil {
		t.Events = make([]*TimeStoneEventWrapper, 0)
	}
	out := make([]*TimeStoneEventWrapper, 0)
	for _, data := range ary {
		wrapper := &TimeStoneEventWrapper{Type: data.GetType()}
		wrapper.UnionData(data)
		nt := []*TimeStoneEventWrapper{wrapper}
		t.Events = append(nt, t.Events...)
		out = append(nt, out...)
	}
	if len(out) > 0 {
		msg := &pb.S2C_OnTimeStoneRecordMessage{
			Records: lo.Map(out, func(item *TimeStoneEventWrapper, index int) *pb.TimeStoneEventWrapper { return item.ToPb() }),
		}
		t.plr.Session.SendNR(pb.S2COnTimeStoneRecordMessage, pb.ProtoMarshalForce(msg))
	}
	t.checkMax()
}
