package structs

import (
	"fmt"
	"math"
	"strings"
	"train/base/cfg"
	"train/base/enum"
	"train/base/enum/black_hole_buff_type"
	"train/base/enum/black_hole_equip_target"
	"train/base/enum/black_hole_layer_type"
	"train/base/enum/black_hole_node_type"
	"train/base/enum/equip_effect_type"
	"train/base/enum/function_type"
	"train/base/enum/store_type"
	"train/common/pb"
	ut "train/utils"
	"train/utils/array"

	"github.com/samber/lo"
	"github.com/spf13/cast"
)

type BlackHoleBuff struct {
	Type    int            `bson:"type"`
	Targets []string       `bson:"targets"`
	Add     map[string]int `bson:"add"`
}

func (this *BlackHoleBuff) ToPb() *pb.BlackHoleBuff {
	m := make(map[string]int32)
	for k, v := range this.Add {
		m[k] = cast.ToInt32(v)
	}
	return &pb.BlackHoleBuff{
		Type:    cast.ToInt32(this.Type),
		Targets: this.Targets,
		Add:     m,
	}
}

type BlackHoleEquip struct {
	Id     int                      `bson:"id"`
	Target int                      `bson:"target"`
	Level  int                      `bson:"level"`
	json   *cfg.BlackHoleEquip[int] `bson:"-"`
}

func (this *BlackHoleEquip) GetJson() *cfg.BlackHoleEquip[int] {
	if this.json == nil {
		this.json, _ = cfg.BlackHoleEquipContainer.GetBeanById(this.Id)
	}
	return this.json
}

func (this *BlackHoleEquip) ToPb() *pb.BlackHoleEquip {
	return &pb.BlackHoleEquip{
		Id:     cast.ToInt32(this.Id),
		Target: cast.ToInt32(this.Target),
		Level:  cast.ToInt32(this.Level),
	}
}

type BlackHoleNode struct {
	Id        string                      `bson:"id"`
	Type      int                         `bson:"type"`
	Enemies   []*BattleRole               `bson:"enemies"`
	Buffs     []*BlackHoleBuff            `bson:"buffs"`
	Equips    []*BlackHoleEquip           `bson:"equips"`
	Aids      []*BattleRole               `bson:"aids"`
	IsFog     bool                        `bson:"isFog"`
	Rewards   []*Condition                `bson:"rewards"`
	json      *cfg.BlackHoleLayer[string] `bson:"-"`
	BlackHole *BlackHole                  `bson:"-" json:"-"`
	Layer     int                         `bson:"layer"`
	Level     int                         `bson:"level"`
}

func (this *BlackHoleNode) Copy() *BlackHoleNode {
	v := &BlackHoleNode{
		Id:      this.Id,
		Type:    this.Type,
		Enemies: array.SliceCopy(this.Enemies),
		Buffs:   array.SliceCopy(this.Buffs),
		Equips:  array.SliceCopy(this.Equips),
		Aids:    array.SliceCopy(this.Aids),
		IsFog:   this.IsFog,
		Rewards: array.SliceCopy(this.Rewards),
		Layer:   this.Layer,
		Level:   this.Level,
	}
	return v
}

func (this *BlackHoleNode) GetJson() *cfg.BlackHoleLayer[string] {
	if this.json == nil {
		this.json, _ = cfg.BlackHoleLayerContainer.GetBeanByUnique(fmt.Sprintf("%d-%d", this.Level, this.Layer))
	}
	return this.json
}

func (this *BlackHoleNode) IsNext(nodeId string) bool {
	args := strings.Split(nodeId, "-")
	nextLayer := cast.ToInt(args[0])
	nextIndex := cast.ToInt(args[1])
	if nextLayer != this.Layer+1 {
		return false
	}
	nextBean, _ := cfg.BlackHoleLayerContainer.GetBeanByUnique(fmt.Sprintf("%d-%d", this.Level, nextLayer))
	if nextBean == nil {
		return false
	}
	args1 := strings.Split(this.Id, "-")
	layerIndex := cast.ToInt(args1[1])
	l := ut.Max(1, layerIndex-1)
	r := ut.Min(nextBean.Count, layerIndex)
	if this.GetJson().Count <= nextBean.Count {
		l = layerIndex
		r = ut.Min(nextBean.Count, layerIndex+1)
	}
	return l <= nextIndex && nextIndex <= r
}

type BlackHoleBoss struct {
	Level int           `bson:"level"`
	Roles []*BattleRole `bson:"roles"`
}

func (this *BlackHoleBoss) ToPb() *pb.BlackHoleBoss {
	return &pb.BlackHoleBoss{
		Level: cast.ToInt32(this.Level),
		Roles: lo.Map(this.Roles, func(e *BattleRole, i int) *pb.BattleRole { return e.ToPb() }),
	}
}

func (this *BlackHoleNode) ToPb() *pb.BlackHoleNode {
	return &pb.BlackHoleNode{
		Id:      this.Id,
		Type:    cast.ToInt32(this.Type),
		Enemies: lo.Map(this.Enemies, func(e *BattleRole, i int) *pb.BattleRole { return e.ToPb() }),
		Buffs:   lo.Map(this.Buffs, func(e *BlackHoleBuff, i int) *pb.BlackHoleBuff { return e.ToPb() }),
		Aids:    lo.Map(this.Aids, func(e *BattleRole, i int) *pb.BattleRole { return e.ToPb() }),
		IsFog:   this.IsFog,
		Reward:  lo.Map(this.Rewards, func(e *Condition, i int) *pb.Condition { return e.ToPb() }),
		Equips:  lo.Map(this.Equips, func(e *BlackHoleEquip, i int) *pb.BlackHoleEquip { return e.ToPb() }),
	}
}

type BlackHole struct {
	Map      []*BlackHoleNode  `bson:"map"`
	CurId    string            `bson:"curId"`
	NextId   string            `bson:"nextId"`
	Roles    []*BattleRole     `bson:"roles"`
	Buffs    []*BlackHoleBuff  `bson:"buffs"`
	Aids     []*BattleRole     `bson:"aids"`
	Equips   []*BlackHoleEquip `bson:"equips"`
	Deads    []string          `bson:"deads"`
	Team     []string          `bson:"team"`
	plr      *Player           `bson:"-"`
	Level    int               `bson:"level"` //当前难度
	Bosses   []*BlackHoleBoss  `bson:"boss"`
	Currency int               `bson:"currency"` //星海币
	Add      float64           `bson:"add"`      //加成
	IsUnlock bool              `bson:"isUnlock"` //是否解锁

}

func (this *BlackHole) ToPb() *pb.BlackHole {
	return &pb.BlackHole{
		CurId:    this.CurId,
		NextId:   this.NextId,
		Map:      lo.Map(this.Map, func(e *BlackHoleNode, i int) *pb.BlackHoleNode { return e.ToPb() }),
		Buffs:    lo.Map(this.Buffs, func(e *BlackHoleBuff, i int) *pb.BlackHoleBuff { return e.ToPb() }),
		Roles:    lo.Map(this.Roles, func(e *BattleRole, i int) *pb.BattleRole { return e.ToPb() }),
		Equips:   lo.Map(this.Equips, func(e *BlackHoleEquip, i int) *pb.BlackHoleEquip { return e.ToPb() }),
		Aids:     lo.Map(this.Aids, func(e *BattleRole, i int) *pb.BattleRole { return e.ToPb() }),
		Deads:    this.Deads,
		Team:     &pb.BattleTeam{Id: 10, Uids: this.Team},
		Level:    cast.ToInt32(this.Level),
		Bosses:   lo.Map(this.Bosses, func(e *BlackHoleBoss, i int) *pb.BlackHoleBoss { return e.ToPb() }),
		Currency: cast.ToInt32(this.Currency),
		Add:      this.Add,
		IsUnlock: this.IsUnlock,
	}
}

func NewBlackHole() *BlackHole {
	return &BlackHole{}
}

func (this *BlackHole) Init(plr *Player) {
	this.plr = plr
	for _, node := range this.Map {
		node.BlackHole = this
	}
}

func (this *BlackHole) CheckAndRefresh() {
	if !this.plr.IsUnlockFunction(function_type.BLACK_HOLE) {
		return
	}
	this.Refresh()
}

func (this *BlackHole) Start(level int) {
	this.Level = level
	this.Add = this.GetEquipAdd() * this.GetPassengerAvgStarLv()
	this.GenMap()
	this.CurId = this.Map[0].Id
}

func (this *BlackHole) Unlock() {
	store := this.plr.Store.Get(store_type.BLACK_HOLE)
	store.Refresh()
	this.plr.PushNew(enum.MARKNEW_NPC_DIALOG, []int{4002})
	this.IsUnlock = true
}

func (this *BlackHole) Refresh() {
	this.Level = 0

	this.Map = make([]*BlackHoleNode, 0)
	this.Buffs = this.Buffs[:0]
	this.Aids = this.Aids[:0]
	this.Deads = this.Deads[:0]
	this.Team = this.Team[:0]
	this.Roles = this.Roles[:0]
	this.Equips = this.Equips[:0]
	this.Bosses = this.Bosses[:0]
	this.NextId = ""
	this.Add = 0
}

func (this *BlackHole) GetNode(id string) *BlackHoleNode {
	return array.Find(this.Map, func(n *BlackHoleNode) bool {
		return n.Id == id
	})
}

func (this *BlackHole) GenMap() {
	this.Map = make([]*BlackHoleNode, 0)
	datas := lo.Filter(cfg.BlackHoleLayerContainer.GetData(), func(data *cfg.BlackHoleLayer[string], _ int) bool {
		return data.Level == this.Level
	})
	for _, data := range datas {
		for j := 1; j <= data.Count; j++ {
			this.Map = append(this.Map, this.GenNode(data, j))
		}
		this.RandomFogs(data.Layer)
	}
}

func (this *BlackHole) RandomFogs(layer int) {
	bean, _ := cfg.BlackHoleLayerContainer.GetBeanByUnique(fmt.Sprintf("%d-%d", this.Level, layer))
	count := bean.Fog

	nodes := lo.Filter(this.Map, func(n *BlackHoleNode, i int) bool { return n.GetJson().Layer == layer })
	nodes = ut.RandomArray(nodes)
	nodes = array.Slice(nodes, 0, count)
	for _, node := range nodes {
		node.IsFog = true
	}
}

func (this *BlackHole) GenNode(bean *cfg.BlackHoleLayer[string], index int) *BlackHoleNode {
	layerType := bean.Type

	Type := black_hole_node_type.START
	switch layerType {
	case black_hole_layer_type.START:
		Type = black_hole_node_type.START
	case black_hole_layer_type.BATTLE:
		Type = black_hole_node_type.BATTLE
	case black_hole_layer_type.REST:
		if index == 1 {
			Type = black_hole_node_type.REBIRTH
		} else {
			Type = black_hole_node_type.AID
		}
	case black_hole_layer_type.BOX:
		Type = black_hole_node_type.BOX
	case black_hole_layer_type.END:
		Type = black_hole_node_type.END
	}

	id := fmt.Sprintf("%d-%d", bean.Layer, index)
	node := &BlackHoleNode{Id: id, Type: Type, BlackHole: this, Layer: bean.Layer, Level: this.Level}

	lvAvg := this.plr.GetAvgLv()
	equipAvgLv := this.GetPassengerAvgEquipLv()
	lv := lvAvg + equipAvgLv
	starLvAvg := this.GetPassengerAvgStarLv()

	layerBean, _ := cfg.BlackHoleLayerContainer.GetBeanByUnique(fmt.Sprintf("%d-%d", this.Level, bean.Layer))

	isBoss := bean.IsBoss()

	if Type == black_hole_node_type.BATTLE {
		enemyCnt := layerBean.C
		enemyRate := (1 + layerBean.A) * starLvAvg

		enemies := make([]*BattleRole, enemyCnt)

		newBattleRole := func(id int, lv int, attrRate float64) *BattleRole {
			return &BattleRole{Uid: fmt.Sprintf("%d_%s", id, ut.ID()), Id: id, Lv: lv, AttrRate: attrRate}
		}

		if isBoss {
			boss := this.GetBossByLv(this.Level)
			if boss == nil {
				enemies = this.GenBoss()
			} else {
				enemies = boss.Roles
				this.Bosses = array.Remove(this.Bosses, boss)
			}

			enemies = lo.Map(enemies, func(e *BattleRole, i int) *BattleRole {
				return newBattleRole(e.Id, lv, enemyRate)
			})
			this.Bosses = append(this.Bosses, &BlackHoleBoss{Level: this.Level, Roles: array.SliceCopy(enemies)})
		} else {
			random := func(index int) {
				noMonsters := GetNoMonsters(enemyCnt, index, lv)

				//先筛已选，需要过滤的
				battleJackPotDatas := cfg.CharacterContainer.GetData()
				battleJackPotDatas = lo.Filter(battleJackPotDatas, func(data *cfg.Character[int], i int) bool {
					if array.Some(enemies, func(m *BattleRole) bool { return m != nil && m.Id == data.Id }) {
						return false
					}
					if array.Has(noMonsters, data.Id) {
						return false
					}
					return true
				})

				rIndex := ut.RandomIndexByWeight(battleJackPotDatas, func(d *cfg.Character[int]) int { return d.Weight })
				data := battleJackPotDatas[rIndex]

				enemies[index] = newBattleRole(data.Id, lv, enemyRate)
			}

			for i := 0; i < enemyCnt; i++ {
				index := ut.Ceil(float64(i) / 2.0)
				if i%2 == 0 {
					random(index)
				} else {
					random(enemyCnt - index)
				}
			}
		}

		node.Enemies = enemies
	} else if Type == black_hole_node_type.AID {
		node.Aids = lo.Map(this.RandomAids(lvAvg), func(id int, i int) *BattleRole {
			return &BattleRole{Uid: fmt.Sprintf("%d_%s", id, ut.ID()), Id: id, Lv: lv, AttrRate: starLvAvg}
		})
	}

	if Type == black_hole_node_type.REBIRTH {
		count := 1
		datas := lo.Filter(cfg.BlackHoleAddContainer.GetData(), func(d *cfg.BlackHoleAdd[string], i int) bool { return d.Type == Type })

		rdDatas := make([]*cfg.BlackHoleAdd[string], 0)

		for i := 0; i < count && len(datas) > 0; i++ {
			index := ut.RandomIndexByWeight(datas, func(d *cfg.BlackHoleAdd[string]) int { return cast.ToInt(d.Add["weight"]) })
			rdDatas = append(rdDatas, datas[index])
			datas = array.RemoveIndex(datas, index)
		}
		add := this.Add

		node.Buffs = lo.Map(rdDatas, func(data *cfg.BlackHoleAdd[string], i int) *BlackHoleBuff {
			buff := &BlackHoleBuff{Add: make(map[string]int, 0)}
			buff.Type = cast.ToInt(data.Add["type"])
			attrs := []string{enum.RoleHP, enum.RoleAttack}
			buff.Add["count"] = cast.ToInt(data.Add["count"])
			for _, attrType := range attrs {
				val := cast.ToFloat64(data.Add[attrType])
				if val <= 0 {
					continue
				}
				attr := float64(cfg.GetRoleAttr(enum.BLACK_HOLE_ATTR_ID, lv, 0, attrType)) * add
				buff.Add[attrType] = ut.Max(1, ut.Round(val*attr))
			}
			return buff
		})
	}

	// 奖励
	node.Rewards = lo.Map(layerBean.Reward, func(m *cfg.ChestReward, i int) *Condition {
		cond := this.plr.ChestRewardToCondition(m)
		if IsOutputCond(cond) {
			cond.Num += ut.Round(float64(cond.Num) * ut.RandomFloat64(-0.1, 0.1))
		}
		return cond
	})

	return node
}

func (this *BlackHole) RandomAids(lvAvg int) []int {
	battleJackPotDatas := cfg.CharacterContainer.GetData()
	datas := lo.Map(battleJackPotDatas, func(d *cfg.Character[int], i int) *cfg.Character[int] {
		passenger := this.plr.GetPassengerById(d.Id)
		weight := float64(d.Weight)
		if passenger != nil {
			lv := passenger.Level
			weight *= math.Min(0.8, float64(lvAvg)/float64(lv*lv))
		}
		return &cfg.Character[int]{Id: d.Id, Weight: ut.Round(weight * 100)}
	})
	res := make([]int, 0)
	for i := 0; i < 3 && len(datas) > 0; i++ {
		rdIndex := ut.RandomIndexByWeight(datas, func(d *cfg.Character[int]) int { return d.Weight })
		id := datas[rdIndex].Id
		datas = array.RemoveIndex(datas, rdIndex)
		res = append(res, id)
	}
	return res
}

func (this *BlackHole) MoveTo(nodeId string) {
	this.CurId = nodeId
	this.NextId = ""
}

func (this *BlackHole) AddBuff(orgBuff *BlackHoleBuff, data ...interface{}) *BlackHoleBuff {
	count := orgBuff.Add["count"]
	buff := ut.Clone(orgBuff).(*BlackHoleBuff)
	buffType := orgBuff.Type
	if buffType == black_hole_buff_type.SELECT {
		targets := data[0].([]string)
		buff.Targets = array.Slice(targets, 0, count)
	} else if buffType == black_hole_buff_type.RANDOM {
		uids := this.GetLiveUids()
		uids = ut.RandomArray(uids)
		buff.Targets = array.Slice(uids, 0, count)
	}

	var allBuff *BlackHoleBuff = nil
	if buffType == black_hole_buff_type.ALL {
		allBuff = array.Find(this.Buffs, func(b *BlackHoleBuff) bool { return b.Type == black_hole_buff_type.ALL })
	}
	if allBuff != nil {
		allBuff.Add[enum.RoleAttack] += buff.Add[enum.RoleAttack]
		allBuff.Add[enum.RoleHP] += buff.Add[enum.RoleHP]
	} else {
		this.Buffs = append(this.Buffs, buff)
	}
	return buff
}

func (this *BlackHole) RandomRebirth() string {
	index := ut.Random(0, len(this.Deads)-1)
	uid := this.Deads[index]
	this.Deads = array.RemoveIndex(this.Deads, index)
	return uid
}

func (this *BlackHole) GetRoleUids() []string {
	pUids := lo.Map(this.Roles, func(p *BattleRole, i int) string { return p.Uid })
	aUids := lo.Map(this.Aids, func(p *BattleRole, i int) string { return p.Uid })
	return append(pUids, aUids...)
}

func (this *BlackHole) GetLiveUids() []string {
	return lo.Filter(this.GetRoleUids(), func(uid string, i int) bool { return !array.Has(this.Deads, uid) })
}

func (this *BlackHole) getBuffAttr(attr string, uid string) int {
	sum := 0
	for _, buff := range this.Buffs {
		if buff.Type != black_hole_buff_type.ALL && !array.Has(buff.Targets, uid) {
			continue
		}
		sum += buff.Add[attr]
	}
	return sum
}

func (this *BlackHole) GetMaxUnlockLevel() int {
	datas := cfg.BlackHoleLevelContainer.GetData()
	datas = lo.Filter(datas, func(data *cfg.BlackHoleLevel[int], _ int) bool { return len(data.Equip) > 0 })
	return datas[len(datas)-1].Id
}

func (this *BlackHole) GetBossByLv(level int) *BlackHoleBoss {
	return array.Find(this.Bosses, func(b *BlackHoleBoss) bool { return b.Level == level })
}

func (this *BlackHole) GenBoss() []*BattleRole {
	datas := cfg.TowerMonsterContainer.GetData()
	datas = lo.Filter(datas, func(data *cfg.TowerMonster[string], _ int) bool {
		if !data.IsBoss() {
			return false
		}

		size := len(data.Monster)

		acc := lo.Reduce(data.Monster, func(acc int, m *cfg.Monster, _ int) int {
			bean, _ := cfg.PlanetMonsterContainer.GetBeanById(m.Id)
			b, _ := cfg.CharacterContainer.GetBeanById(bean.GetPassengerId())
			if b == nil {
				return acc
			}
			return acc + 1
		}, 0)
		return size == acc
	})
	rdIndex := ut.Random(0, len(datas)-1)
	data := datas[rdIndex]
	ary := lo.Map(data.Monster, func(m *cfg.Monster, i int) *BattleRole {
		bean, _ := cfg.PlanetMonsterContainer.GetBeanById(m.Id)
		return &BattleRole{Id: bean.GetPassengerId()}
	})
	return ary
}

func (this *BlackHole) GenEquips() []*BlackHoleEquip {
	curEquips := this.Equips
	datas := make([]*BlackHoleEquip, 0)

	typeCount := 3
	for _, data := range cfg.BlackHoleEquipContainer.GetData() {
		if data.Target == black_hole_equip_target.NONE || data.Weight <= 0 {
			continue
		}
		if data.Target == black_hole_equip_target.BATTLE_TYPE || data.Target == black_hole_equip_target.ANIMAL_TYPE {
			count := 3
			for i := 1; i <= count; i++ {
				has := array.Some(curEquips, func(e *BlackHoleEquip) bool {
					return e.Id == data.Id && e.Target == i
				})
				if !has {
					equip := &BlackHoleEquip{Id: data.Id, Target: i}
					datas = append(datas, equip)
				}
			}
		} else {
			has := array.Some(curEquips, func(e *BlackHoleEquip) bool {
				return e.Id == data.Id
			})
			if !has {
				datas = append(datas, &BlackHoleEquip{Id: data.Id})
			}
		}
	}
	res := make([]*BlackHoleEquip, 0)

	getWeight := func(e *BlackHoleEquip) int {
		weight := e.GetJson().Weight * 1000
		targetType := e.GetJson().Target
		roleBeans := cfg.CharacterContainer.GetData()
		totCount := 0
		totWeight := 0.0
		counts := make([]int, 0)
		if targetType == black_hole_equip_target.BATTLE_TYPE {
			for i := 1; i <= typeCount; i++ {
				count := lo.CountBy(roleBeans, func(r *cfg.Character[int]) bool { return r.BattleType == i })
				counts = append(counts, count)
				totCount += count
			}
		} else if targetType == black_hole_equip_target.ANIMAL_TYPE {
			for i := 1; i <= typeCount; i++ {
				count := lo.CountBy(roleBeans, func(r *cfg.Character[int]) bool { return r.AnimalType == i })
				counts = append(counts, count)
				totCount += count
			}
		}
		if len(counts) > 0 {
			for _, count := range counts {
				totWeight += float64(totCount) / float64(count)
			}
			cur := counts[e.Target-1]
			weight = ut.Round(float64(totCount) / float64(cur) * float64(weight*len(counts)) / float64(totWeight))
		}
		return weight
	}

	lv := this.GetAvgLv()
	for i := 0; i < 3 && len(datas) > 0; i++ {
		rdIndex := ut.RandomIndexByWeight(datas, func(e *BlackHoleEquip) int {
			weight := getWeight(e)
			// log.Debug("%v %v %v", e.Id, e.Target, weight)
			return weight
		})
		data := datas[rdIndex]
		data.Level = lv
		res = append(res, data)
		datas = array.RemoveIndex(datas, rdIndex)
	}
	return res
}

func (this *BlackHole) GetEquipAdd() float64 {
	return 0.65
}

func (this *BlackHole) GetAvgLv() int {
	return this.plr.GetAvgLv() + this.GetPassengerAvgEquipLv()
}

func (this *BlackHole) GetPassengerAvgStarLv() float64 {
	total := 0
	roles := this.Roles
	for _, role := range roles {
		total += role.StarLv
	}
	star := float64(total) / float64(len(roles))
	return 1 + star*cfg.GetMisc().StarLvAttrRatio
}

func (this *BlackHole) GetPassengerAvgEquipLv() int {
	total := 0.0
	for _, role := range this.Roles {
		for _, equip := range role.Equips {
			if equip != nil {
				for _, e := range equip.Effects {
					if e.GetJson().Type == equip_effect_type.ATTR {
						total += float64(e.Level) * 0.4
					} else {
						total += float64(e.Level) * 0.2 * 5
					}
				}
			}
		}
		// equip := array.Find(role.Equips, func(e *EquipItem) bool {
		// 	return e.GetJson().Index == index
		// })
	}
	return ut.Round(total / float64(len(this.Roles)))
}
