package enum

const (
	RoleHP     = "hp"
	RoleAttack = "attack"
)

// 任务触发时机类型
const (
	TriggerUnlockFunc       = "UNLOCKFUNC"         // 指定新手引导功能解锁时触发 -> OnPlayerFunctionOpen
	TriggerGuideUnlockFunc  = "GUIDE_UNLOCKFUNC"   // 指定新手引导功能解锁时触发 -> OnPlayerFunctionOpen
	TriggerGuideComplateId  = "GUIDE_COMPLATE_ID"  // 新手引导指定步骤时触发
	TriggerReachPlanet      = "REACH_PLANET"       // 抵达指定星球后触发
	TriggerComplatePlanet   = "COMPLATE_PLANET"    // 完成星球后触发
	TriggerComplatePlanetId = "COMPLATE_PLANET_ID" //完成星球节点后触发
	TriggerComplateBattle   = "COMPLATE_BATTLE"    // 完成某场战斗时解锁
	TriggerBuildCarriage    = "BUILT_CARRIAGE"     // 建造指定车厢后触发 ->  EvtOnPlayerBuildCarriageEnd
	TriggerCompleteTheme    = "COMPLETE_THEME"     // 完成主题下的设施建设
	TriggerUnlockTheme      = "UNLOCK_THEME"       // 解锁主题
)

const (
	QualityGray   = iota // 灰色品质
	QualityGreen         // 绿色品质
	QualityBlue          // 蓝色品质
	QualityPerple        // 紫色品质
	QualityOrange        // 橙色品质
)

const (
	ToolPlantCollect   = iota + 1 // 植物采集类型
	ToolMineralCollect            // 矿物采集类型
	ToolPartsCollect              // 零件拆解类型
)

const (
	SkillAddition1  = 1  //角色产出加成-小费
	SkillAddition2  = 2  //角色产出加成-爱心
	SkillAddition21 = 21 //列车设施出加成-星尘
	SkillAddition23 = 23 //列车设施出加成-电力
	SkillAddition24 = 24 //列车设施出加成-净水
)

// new标签类型
const (
	MARKNEW_PROP              = 1  //新获得的关键道具
	MARKNEW_THEME             = 2  //新解锁的主题
	MARKNEW_ROLE_NEW          = 3  //新获得的角色
	MARKNEW_ROLE_UNREAD       = 4  //没有阅读过角色档案
	MARKNEW_BUILD_CAN_LVUP    = 5  //设施可以继续升级
	MARKNEW_BUILD_UNLOCK_SKIN = 6  //设施解锁新外观
	MARKNEW_PROP_USE          = 7  //新获得的关键道具,是否使用过
	MARKNEW_NPC_DIALOG        = 8  //npc对话
	MARKNEW_PLANET            = 9  //星球
	MARKNEW_SEED              = 10 // 种子
)

const (
	PER = "PER"
	INT = "INT"
)

const (
	HOT_UPDATE       = 0
	BIG_UPDATE       = 1
	NOT_UPDATE       = 2
	INVALID_PLATFORM = 3
)

const (
	BLACK_HOLE_ATTR_ID = 0
)
