package pb

// msg_define 自动生成，不要在这个文件添加任何内容。

// S2CClaimAchievementRewardMessage 领取成就奖励结果
const S2CClaimAchievementRewardMessage = "S2C_ClaimAchievementRewardMessage" 
// S2CAcceptArrestMessage 领取通缉令任务
const S2CAcceptArrestMessage = "S2C_AcceptArrestMessage" 
// S2CSetArrestBattleResultMessage 通缉令战斗结果
const S2CSetArrestBattleResultMessage = "S2C_SetArrestBattleResultMessage" 
// S2CFinishArrestMessage 通缉令任务领奖
const S2CFinishArrestMessage = "S2C_FinishArrestMessage" 
// S2CDestroyArrestMessage 销毁通缉令
const S2CDestroyArrestMessage = "S2C_DestroyArrestMessage" 
// S2CRefreshAllArrestMessage 刷新通缉令
const S2CRefreshAllArrestMessage = "S2C_RefreshAllArrestMessage" 
// S2CShowArrestResultMessage 展示战报
const S2CShowArrestResultMessage = "S2C_ShowArrestResultMessage" 
// S2CTicketMergeMessage 合成车票
const S2CTicketMergeMessage = "S2C_TicketMergeMessage" 
// S2CDropItemMessage 丢弃物品
const S2CDropItemMessage = "S2C_DropItemMessage" 
// S2CSpaceStoneLvUpMessage 空间宝石升级
const S2CSpaceStoneLvUpMessage = "S2C_SpaceStoneLvUpMessage" 
// S2CUseSpaceStoneMessage 使用空间宝石传送
const S2CUseSpaceStoneMessage = "S2C_UseSpaceStoneMessage" 
// S2CMarkSpaceStoneMessage 使用空间宝石标记
const S2CMarkSpaceStoneMessage = "S2C_MarkSpaceStoneMessage" 
// S2CSyncItemMessage 同步物品
const S2CSyncItemMessage = "S2C_SyncItemMessage" 
// S2CUseItemMessage 使用物品
const S2CUseItemMessage = "S2C_UseItemMessage" 
// S2CTimeStoneLvUpMessage 时间宝石升级
const S2CTimeStoneLvUpMessage = "S2C_TimeStoneLvUpMessage" 
// S2CTimeStoneRecordMessage 时间宝石记录
const S2CTimeStoneRecordMessage = "S2C_TimeStoneRecordMessage" 
// S2COnTimeStoneRecordMessage 时间宝石记录下发
const S2COnTimeStoneRecordMessage = "S2C_OnTimeStoneRecordMessage" 
// S2CTimeStoneUseMessage 使用时间宝石
const S2CTimeStoneUseMessage = "S2C_TimeStoneUseMessage" 
// S2CReadyStartBlackHoleMessage 预加载黑洞的一些信息
const S2CReadyStartBlackHoleMessage = "S2C_ReadyStartBlackHoleMessage" 
// S2CStartBlackHoleMessage 开始黑洞玩法
const S2CStartBlackHoleMessage = "S2C_StartBlackHoleMessage" 
// S2CSelectBlackHoleNodeMessage 选择下一步节点
const S2CSelectBlackHoleNodeMessage = "S2C_SelectBlackHoleNodeMessage" 
// S2CSyncBlackHoleMessage 同步黑洞数据
const S2CSyncBlackHoleMessage = "S2C_SyncBlackHoleMessage" 
// S2CUnlockBlackHoleMessage 解锁黑洞
const S2CUnlockBlackHoleMessage = "S2C_UnlockBlackHoleMessage" 
// S2CSyncAllBurstTaskMessage 同步任务数据
const S2CSyncAllBurstTaskMessage = "S2C_SyncAllBurstTaskMessage" 
// S2CStartBurstTaskMessage 开始任务
const S2CStartBurstTaskMessage = "S2C_StartBurstTaskMessage" 
// S2CClaimBurstTaskRewardMessage 领取任务奖励
const S2CClaimBurstTaskRewardMessage = "S2C_ClaimBurstTaskRewardMessage" 
// S2CCollectMapMineDoneMessage 砍倒采集物
const S2CCollectMapMineDoneMessage = "S2C_CollectMapMineDoneMessage" 
// S2CFinishDailyTaskMessage 完成每日任务
const S2CFinishDailyTaskMessage = "S2C_FinishDailyTaskMessage" 
// S2CSyncDailyTaskInfoMessage 同步每日任务模块数据
const S2CSyncDailyTaskInfoMessage = "S2C_SyncDailyTaskInfoMessage" 
// S2CDialogTaskDoneMessage 完成任务对话
const S2CDialogTaskDoneMessage = "S2C_DialogTaskDoneMessage" 
// S2CBattleTaskDoneTestMessage 完成战斗任务
const S2CBattleTaskDoneTestMessage = "S2C_BattleTaskDoneTestMessage" 
// S2CWearEquipMessage 穿戴装备结果
const S2CWearEquipMessage = "S2C_WearEquipMessage" 
// S2CUnWearEquipMessage 卸下装备结果
const S2CUnWearEquipMessage = "S2C_UnWearEquipMessage" 
// S2CMakeEquipMessage 打造装备
const S2CMakeEquipMessage = "S2C_MakeEquipMessage" 
// S2CBuyEquipMessage 购买装备
const S2CBuyEquipMessage = "S2C_BuyEquipMessage" 
// S2CSellEquipMessage 出售装备
const S2CSellEquipMessage = "S2C_SellEquipMessage" 
// S2CSyncExploreMessage 同步探索状态
const S2CSyncExploreMessage = "S2C_SyncExploreMessage" 
// S2CStartExploreMessage 开始探索
const S2CStartExploreMessage = "S2C_StartExploreMessage" 
// S2CClaimExploreRewardMessage 领取探索奖励
const S2CClaimExploreRewardMessage = "S2C_ClaimExploreRewardMessage" 
// S2CGetExploreAreaMessage 获取要探索的区域
const S2CGetExploreAreaMessage = "S2C_GetExploreAreaMessage" 
// S2CCeilOperationMessage 操作结果
const S2CCeilOperationMessage = "S2C_CeilOperationMessage" 
// S2CCeilSyncMessage 格子数据同步
const S2CCeilSyncMessage = "S2C_CeilSyncMessage" 
// S2CInstanceFightMessage 副本挑战结果(临时使用)
const S2CInstanceFightMessage = "S2C_InstanceFightMessage" 
// S2CSyncInstanceMessage 同步副本数据
const S2CSyncInstanceMessage = "S2C_SyncInstanceMessage" 
// S2CUnlockInstanceMessage 解锁副本
const S2CUnlockInstanceMessage = "S2C_UnlockInstanceMessage" 
// S2CCompleteInstancePuzzleMessage 完成解密
const S2CCompleteInstancePuzzleMessage = "S2C_CompleteInstancePuzzleMessage" 
// S2CErrorResultMessage 通用返回消息
const S2CErrorResultMessage = "S2C_ErrorResultMessage" 
// S2CRegisterResultMessage 注册结果
const S2CRegisterResultMessage = "S2C_RegisterResultMessage" 
// S2CCertificationResultMessage 实名认证结果
const S2CCertificationResultMessage = "S2C_CertificationResultMessage" 
// S2CLoginResultMessage 登录结果
const S2CLoginResultMessage = "S2C_LoginResultMessage" 
// S2CBindResultMessage 绑定结果
const S2CBindResultMessage = "S2C_BindResultMessage" 
// S2CNoticeMessage 测试消息
const S2CNoticeMessage = "S2C_NoticeMessage" 
// S2CEnterGameServerResultMessage 选区结果
const S2CEnterGameServerResultMessage = "S2C_EnterGameServerResultMessage" 
// S2CGetPlayerInfoResMessage 玩家基础数据消息返回
const S2CGetPlayerInfoResMessage = "S2C_GetPlayerInfoResMessage" 
// S2CCurrencyChangeMessage 货币变化消息
const S2CCurrencyChangeMessage = "S2C_CurrencyChangeMessage" 
// S2CBagItemChangeMessage 背包道具变化消息
const S2CBagItemChangeMessage = "S2C_BagItemChangeMessage" 
// S2CGmExecuteRspMessage 执行Gm命令返回(可有可无)
const S2CGmExecuteRspMessage = "S2C_GmExecuteRspMessage" 
// S2CJackpotRspMessage 抽卡结果
const S2CJackpotRspMessage = "S2C_JackpotRspMessage" 
// S2CCollectItemRespMessage 收取资源
const S2CCollectItemRespMessage = "S2C_CollectItemRespMessage" 
// S2CSpeedUpMessage 开始使用加速
const S2CSpeedUpMessage = "S2C_SpeedUpMessage" 
// S2CStopSpeedUpMessage 停止使用加速
const S2CStopSpeedUpMessage = "S2C_StopSpeedUpMessage" 
// S2CSyncSpeedUpMessage 同步加速状态
const S2CSyncSpeedUpMessage = "S2C_SyncSpeedUpMessage" 
// S2CRecoverEnergyRespMessage 恢复加速能量
const S2CRecoverEnergyRespMessage = "S2C_RecoverEnergyRespMessage" 
// S2CSyncMessage 与客户端同步部分数值
const S2CSyncMessage = "S2C_SyncMessage" 
// S2CLogoutMessage 通知客户端登出,这个消息发送完毕后session会立即关闭
const S2CLogoutMessage = "S2C_LogoutMessage" 
// S2CRecordGuideStepResultMessage 记录引导数据结果
const S2CRecordGuideStepResultMessage = "S2C_RecordGuideStepResultMessage" 
// S2CClaimTaskRewardResultMessage 领取任务奖励结果
const S2CClaimTaskRewardResultMessage = "S2C_ClaimTaskRewardResultMessage" 
// S2CSyncPlanetMessage 同步当前星球数据
const S2CSyncPlanetMessage = "S2C_SyncPlanetMessage" 
// S2CSyncDailyInfoRespMessage 同步每日刷新的数据
const S2CSyncDailyInfoRespMessage = "S2C_SyncDailyInfoRespMessage" 
// S2CMailDetailRespMessage 获取邮件详情结果
const S2CMailDetailRespMessage = "S2C_MailDetailRespMessage" 
// S2COnNewMailMessage 新邮件提示
const S2COnNewMailMessage = "S2C_OnNewMailMessage" 
// S2CDeleteReadMailRespMessage 删除已读结果
const S2CDeleteReadMailRespMessage = "S2C_DeleteReadMailRespMessage" 
// S2CAttachMailRespMessage 领取邮件附件结果
const S2CAttachMailRespMessage = "S2C_AttachMailRespMessage" 
// S2CMailListRespMessage 拉取邮件列表结果
const S2CMailListRespMessage = "S2C_MailListRespMessage" 
// S2CCheckCdkMessage 兑换码兑换结果
const S2CCheckCdkMessage = "S2C_CheckCdkMessage" 
// S2CDiyPlayerInfoRespMessage 修改个人信息结果
const S2CDiyPlayerInfoRespMessage = "S2C_DiyPlayerInfoRespMessage" 
// S2CSignOutRespMessage 申请注销
const S2CSignOutRespMessage = "S2C_SignOutRespMessage" 
// S2CCancelSignOutRespMessage 取消注销结果
const S2CCancelSignOutRespMessage = "S2C_CancelSignOutRespMessage" 
// S2CRemoveNewMarkMessage 去除new标签
const S2CRemoveNewMarkMessage = "S2C_RemoveNewMarkMessage" 
// S2CBuyBatteryMessage 钻石兑换电池
const S2CBuyBatteryMessage = "S2C_BuyBatteryMessage" 
// S2CSetBattleTeamMessage 设置战斗编队
const S2CSetBattleTeamMessage = "S2C_SetBattleTeamMessage" 
// S2CJackpotPointsGetMessage 抽卡积分领取结果
const S2CJackpotPointsGetMessage = "S2C_JackpotPointsGetMessage" 
// S2CUnlockSpeedUpAutoMessage 解锁自动加速结果
const S2CUnlockSpeedUpAutoMessage = "S2C_UnlockSpeedUpAutoMessage" 
// S2CGetAdRewardMessage 获取广告奖励
const S2CGetAdRewardMessage = "S2C_GetAdRewardMessage" 
// S2COnGetBurstTaskMessage 突发任务触发
const S2COnGetBurstTaskMessage = "S2C_OnGetBurstTaskMessage" 
// S2CSyncEnergyMessage 同步加速能量
const S2CSyncEnergyMessage = "S2C_SyncEnergyMessage" 
// S2COreActionMessage 矿场格子操作
const S2COreActionMessage = "S2C_OreActionMessage" 
// S2COreSyncBreakItemTimeMessage 矿场格子操作
const S2COreSyncBreakItemTimeMessage = "S2C_OreSyncBreakItemTimeMessage" 
// S2COreLevelFightMessage 矿场难度挑战成功
const S2COreLevelFightMessage = "S2C_OreLevelFightMessage" 
// S2CGetOreLevelDataMessage 获取矿场指定难度矿洞的数据
const S2CGetOreLevelDataMessage = "S2C_GetOreLevelDataMessage" 
// S2CUnlockOreMessage 解锁矿场
const S2CUnlockOreMessage = "S2C_UnlockOreMessage" 
// S2CChangePassengerDormRespMessage 修改乘客入住信息结果
const S2CChangePassengerDormRespMessage = "S2C_ChangePassengerDormRespMessage" 
// S2CPassengerLevelUpResultMessage 乘客操作结果
const S2CPassengerLevelUpResultMessage = "S2C_PassengerLevelUpResultMessage" 
// S2CChangePassengerWorkMessage 修改乘客工作信息结果
const S2CChangePassengerWorkMessage = "S2C_ChangePassengerWorkMessage" 
// S2CCompletePassengerPlotMessage 完成乘客剧情
const S2CCompletePassengerPlotMessage = "S2C_CompletePassengerPlotMessage" 
// S2CUnlockSkinMessage 解锁乘客皮肤
const S2CUnlockSkinMessage = "S2C_UnlockSkinMessage" 
// S2CChangeSkinMessage 更换乘客皮肤
const S2CChangeSkinMessage = "S2C_ChangeSkinMessage" 
// S2CTalentLevelUpMessage 升级天赋
const S2CTalentLevelUpMessage = "S2C_TalentLevelUpMessage" 
// S2CFragMergeMessage 投影合成
const S2CFragMergeMessage = "S2C_FragMergeMessage" 
// S2CPassengerUnlockProfileMessage 乘客解锁资料
const S2CPassengerUnlockProfileMessage = "S2C_PassengerUnlockProfileMessage" 
// S2CTransPassengerMessage 乘客转换
const S2CTransPassengerMessage = "S2C_TransPassengerMessage" 
// S2CPassengerProfileSortChangeMessage 乘客资料排序更换
const S2CPassengerProfileSortChangeMessage = "S2C_PassengerProfileSortChangeMessage" 
// S2CCreatePayOrderMessage 创建订单
const S2CCreatePayOrderMessage = "S2C_CreatePayOrderMessage" 
// S2CVerifyPayOrderMessage 验证订单
const S2CVerifyPayOrderMessage = "S2C_VerifyPayOrderMessage" 
// S2CTrainNavigationResultMessage 星球航行结果
const S2CTrainNavigationResultMessage = "S2C_TrainNavigationResultMessage" 
// S2CGetPlanetMoveSurplusTimeRespMessage 获取星球航行剩余时间
const S2CGetPlanetMoveSurplusTimeRespMessage = "S2C_GetPlanetMoveSurplusTimeRespMessage" 
// S2CLandPlanetRespMessage 登陆星球结果
const S2CLandPlanetRespMessage = "S2C_LandPlanetRespMessage" 
// S2CChapterPassResultMessage 通过关卡节点响应
const S2CChapterPassResultMessage = "S2C_ChapterPassResultMessage" 
// S2CPassBranchPlanetNodeMessage 通过星球支线节点
const S2CPassBranchPlanetNodeMessage = "S2C_PassBranchPlanetNodeMessage" 
// S2CClaimBranchPlanetNodeRewardMessage 领取星球节点内的奖励
const S2CClaimBranchPlanetNodeRewardMessage = "S2C_ClaimBranchPlanetNodeRewardMessage" 
// S2CCancelMoveToPlanetMessage 取消航行
const S2CCancelMoveToPlanetMessage = "S2C_CancelMoveToPlanetMessage" 
// S2CUnlockProfileMessage 解锁星球贴纸
const S2CUnlockProfileMessage = "S2C_UnlockProfileMessage" 
// S2CProfileCollectRewardMessage 领取星球贴纸奖励
const S2CProfileCollectRewardMessage = "S2C_ProfileCollectRewardMessage" 
// S2CPlanetProfileSortChangeMessage 星球资料排序更换
const S2CPlanetProfileSortChangeMessage = "S2C_PlanetProfileSortChangeMessage" 
// S2CChapterPassRandomBoxMessage 通过关卡节点响应
const S2CChapterPassRandomBoxMessage = "S2C_ChapterPassRandomBoxMessage" 
// S2CChapterStartTimeLimitBoxMessage 通过关卡节点响应
const S2CChapterStartTimeLimitBoxMessage = "S2C_ChapterStartTimeLimitBoxMessage" 
// S2CChapterSyncTimeLimitBoxMessage 同步时间响应
const S2CChapterSyncTimeLimitBoxMessage = "S2C_ChapterSyncTimeLimitBoxMessage" 
// S2CChapterPassTimeLimitBoxMessage 通过关卡节点响应
const S2CChapterPassTimeLimitBoxMessage = "S2C_ChapterPassTimeLimitBoxMessage" 
// S2CChapterPassMonsterBoxMessage 通过关卡节点响应
const S2CChapterPassMonsterBoxMessage = "S2C_ChapterPassMonsterBoxMessage" 
// S2CChapterPassToolBlessMessage 通过关卡节点响应
const S2CChapterPassToolBlessMessage = "S2C_ChapterPassToolBlessMessage" 
// S2CChapterPassRageModeMessage 通过关卡节点响应
const S2CChapterPassRageModeMessage = "S2C_ChapterPassRageModeMessage" 
// S2CDoPublicityMessage 宣传响应
const S2CDoPublicityMessage = "S2C_DoPublicityMessage" 
// S2CGetPublicityRewardMessage 领取宣传奖励
const S2CGetPublicityRewardMessage = "S2C_GetPublicityRewardMessage" 
// S2CProfileBranchPassNodeMessage 通过节点
const S2CProfileBranchPassNodeMessage = "S2C_ProfileBranchPassNodeMessage" 
// S2CProfileBranchQuestionMessage 开始问答
const S2CProfileBranchQuestionMessage = "S2C_ProfileBranchQuestionMessage" 
// S2CProfileBranchSyncEnergyMessage 同步体力
const S2CProfileBranchSyncEnergyMessage = "S2C_ProfileBranchSyncEnergyMessage" 
// S2CProfileBranchUnlockMessage 开启记忆阁
const S2CProfileBranchUnlockMessage = "S2C_ProfileBranchUnlockMessage" 
// S2CUpdateFormationMessage 更新竞技场阵容
const S2CUpdateFormationMessage = "S2C_UpdateFormationMessage" 
// S2CGetRankListMessage 获取竞技场排名
const S2CGetRankListMessage = "S2C_GetRankListMessage" 
// S2CGetRivalMessage 获取竞技场对手
const S2CGetRivalMessage = "S2C_GetRivalMessage" 
// S2CPvpFightMessage 竞技场挑战
const S2CPvpFightMessage = "S2C_PvpFightMessage" 
// S2CPvpBattleRecordListMessage 获取战绩列表
const S2CPvpBattleRecordListMessage = "S2C_PvpBattleRecordListMessage" 
// S2CPvpBattleReplayMessage pvp战斗重放
const S2CPvpBattleReplayMessage = "S2C_PvpBattleReplayMessage" 
// S2CPvpModuleDataMessage pvp模块数据
const S2CPvpModuleDataMessage = "S2C_PvpModuleDataMessage" 
// S2CSetResonanceRoleMessage 设置共鸣乘客
const S2CSetResonanceRoleMessage = "S2C_SetResonanceRoleMessage" 
// S2CStoreRefreshMessage 刷新商店
const S2CStoreRefreshMessage = "S2C_StoreRefreshMessage" 
// S2CStoreBuyMessage 商店购买
const S2CStoreBuyMessage = "S2C_StoreBuyMessage" 
// S2CSyncStoreMessage 同步商店
const S2CSyncStoreMessage = "S2C_SyncStoreMessage" 
// S2CToolMakeRespMessage 工具打造结果
const S2CToolMakeRespMessage = "S2C_ToolMakeRespMessage" 
// S2CFurnaceUpgradeRespMessage 打造台升级结果
const S2CFurnaceUpgradeRespMessage = "S2C_FurnaceUpgradeRespMessage" 
// S2CTowerBattleMessage 爬塔战斗结果
const S2CTowerBattleMessage = "S2C_TowerBattleMessage" 
// S2CTowerBattleWinMessage 战斗胜利
const S2CTowerBattleWinMessage = "S2C_TowerBattleWinMessage" 
// S2CBuyCarriageResultMessage 解锁车厢结果
const S2CBuyCarriageResultMessage = "S2C_BuyCarriageResultMessage" 
// S2CGetCarriageBuildInfoResMessage 车厢建造完成
const S2CGetCarriageBuildInfoResMessage = "S2C_GetCarriageBuildInfoResMessage" 
// S2COpenCarriageDoorResMessage 车厢门打开
const S2COpenCarriageDoorResMessage = "S2C_OpenCarriageDoorResMessage" 
// S2CBuildLevelUpMessage 升级设施等级
const S2CBuildLevelUpMessage = "S2C_BuildLevelUpMessage" 
// S2CChangeBuildSkinMessage 改变设施皮肤
const S2CChangeBuildSkinMessage = "S2C_ChangeBuildSkinMessage" 
// S2CCarriageThemeLvUpMessage 提升车厢主题等级
const S2CCarriageThemeLvUpMessage = "S2C_CarriageThemeLvUpMessage" 
// S2CUnlockGoodsMessage 解锁货物
const S2CUnlockGoodsMessage = "S2C_UnlockGoodsMessage" 
// S2CLevelUpGoodsMessage 升级货物
const S2CLevelUpGoodsMessage = "S2C_LevelUpGoodsMessage" 
// S2CGetTrainActivityMessage 获取列车活动数据
const S2CGetTrainActivityMessage = "S2C_GetTrainActivityMessage" 
// S2CArrangeTrainActivityMessage 安排列车活动
const S2CArrangeTrainActivityMessage = "S2C_ArrangeTrainActivityMessage" 
// S2CGetTrainActivityRewardMessage 领取列车活动奖励
const S2CGetTrainActivityRewardMessage = "S2C_GetTrainActivityRewardMessage" 
// S2CSyncAllTrainDailyTaskMessage 同步列车日常任务数据
const S2CSyncAllTrainDailyTaskMessage = "S2C_SyncAllTrainDailyTaskMessage" 
// S2CStartTrainDailyTaskMessage 开始列车日常任务
const S2CStartTrainDailyTaskMessage = "S2C_StartTrainDailyTaskMessage" 
// S2CClaimTrainDailyTaskRewardMessage 领取列车日常任务奖励
const S2CClaimTrainDailyTaskRewardMessage = "S2C_ClaimTrainDailyTaskRewardMessage" 
// S2CTrainTechUpgradeMessage 升级列车科技
const S2CTrainTechUpgradeMessage = "S2C_TrainTechUpgradeMessage" 
// S2CTransportStartMessage 领取护送任务
const S2CTransportStartMessage = "S2C_TransportStartMessage" 
// S2CTransportFightMessage 护送挑战(临时使用)
const S2CTransportFightMessage = "S2C_TransportFightMessage" 
// S2CTransportRewardGetMessage 结果
const S2CTransportRewardGetMessage = "S2C_TransportRewardGetMessage" 
// S2CTransportBackMessage 返回雪星
const S2CTransportBackMessage = "S2C_TransportBackMessage" 
// S2CSyncTransportMessage 同步运送数据
const S2CSyncTransportMessage = "S2C_SyncTransportMessage" 
// S2CSyncWantedMessage 同步悬赏状态
const S2CSyncWantedMessage = "S2C_SyncWantedMessage" 
// S2CRefrehWantedMessage 手动刷新悬赏
const S2CRefrehWantedMessage = "S2C_RefrehWantedMessage" 
// S2CStartWantedMessage 开始悬赏
const S2CStartWantedMessage = "S2C_StartWantedMessage" 
// S2CClaimWantedRewardMessage 领取悬赏奖励
const S2CClaimWantedRewardMessage = "S2C_ClaimWantedRewardMessage" 
// S2CSyncAllWantedMessage 同步悬赏数据
const S2CSyncAllWantedMessage = "S2C_SyncAllWantedMessage" 
