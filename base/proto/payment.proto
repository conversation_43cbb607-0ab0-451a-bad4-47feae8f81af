syntax = "proto3";

package proto;

option go_package = "./pb";

import "enum.proto";

//After are enums.
//After are structs.
//After are messages.
message C2S_CreatePayOrderMessage {
  string productId = 1; //商品id
  OrderPayPlatform orderPlatform = 2; //支付平台
}
message S2C_CreatePayOrderMessage {
  int32 code = 1; //0
  map<string,string> options = 2; //数据选项
}
message C2S_VerifyPayOrderMessage {
  string orderId = 1; //订单id
}
message S2C_VerifyPayOrderMessage {
  int32 code = 1; //非0则请求失败
  OrderState state = 2; //订单状态
}
