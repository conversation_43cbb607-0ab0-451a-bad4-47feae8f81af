syntax = "proto3";

package proto;

option go_package = "./pb";

import "enum.proto";

import "google/protobuf/any.proto";

//After are enums.
//After are structs.
// 登录公共信息
message LoginCommon{
  string platform = 1; //平台
  string ver = 2; //客户端当前版本
  string distanceId = 3; //ta访客id
  string os = 4; //系统
  bool closeGuide = 5; //关闭新手引导
  string inviteCode = 6; //邀请码
  string packageSign = 7; //包签名
}

// 用户账号数据信息
message UserInfo{
  string uid = 1; //用户全局唯一id
  string lToken = 2; //登录服token，后续用来免密登录
  int32 age = 3; //年龄
  int32 signOutTime = 4; //注销倒计时
  string nickName = 5; //第三方用户名
  string avatarUrl = 6; //第三方头像
  string openid = 7; //第三方openid
  string userType = 8; //用户类型
}

// 物品基本数据
message ItemInfo{
  string uid = 1; //物品uid
  int32 id = 2; //物品配置
  int32 num = 3; //物品数量
  int32 surplusTime = 4; //剩余时间
}

// 列车数据
message TrainInfo{
  CarriageInfo head = 1; //车头
  repeated CarriageInfo carriages = 2; //车厢
  int32 electricTime = 3; //电力加成剩余时间
  int32 waterTime = 4; //水能加成剩余时间
}

// 车厢产出数据
message CarriageOutput{
  int32 val = 1; //当前累计
  int64 accTotal = 2; //总累计
}

// 车厢数据
message CarriageInfo{
  int32 id = 1; //车厢id
  int32 themeLv = 2; //车厢主题等级
  repeated TrainItemInfo builds = 3; //设施
  int32 buildTime = 4; //建造剩余时间
  bool openDoor = 5; //是否打开猫猫门
  CarriageOutput starOutput = 6; //星尘产出
  CarriageOutput heartOutput = 7; //爱心产出
  CarriageOutput electricOutput = 8; //电力产出
  CarriageOutput waterOutput = 9; //水产出
  int32 outputTime = 10; //产出累计时间
  repeated CarriageGoodsInfo goods = 11; //已解锁货物
  CarriageOutput vitalityOutput = 12; //元气值产出
}

// 设施数据
message TrainItemInfo{
  int32 order = 1; //设施序号
  int32 lv = 2; //设施等级
  int32 skin = 3; //设施皮肤
}

// 条件数据
message Condition{
  int32 id = 1; //条件id
  int32 num = 2; //条件数量
  int32 type = 3; //条件类型
  bool isHide = 4; //是否隐藏
  string extra = 5; //扩展数据
}

// 产出数据
message Output{
  int32 id = 1; //产出对象的id
  repeated Condition items = 2; //道具
}

// 加速能量数据
message Energy{
  int32 energy = 1; //加速能量
  int32 freeRecoverNum = 2; //免费恢复次数
  bool used = 3; //使用过加速
  bool isSpeedUp = 4; //是否正在加速
  bool isUnlockSpeedUpAuto = 5; //是否解锁了自动加速
  int32 nextRecoverTime = 6; //下次恢复时间
}

// 乘客数据
message PassengerInfo{
  int32 id = 1; //乘客配置id
  int32 level = 2; //等级
  int32 starLv = 3; //星级
  int32 exp = 4; //经验
  int32 dormId = 5; //入住车厢
  int32 heartOutput = 6; //爱心产出
  int32 starOutput = 7; //星尘产出
  int32 workId = 8; //工作车厢id
  int32 workIndex = 9; //工位下标
  repeated PassengerPlot plots = 10; //剧情
  int32 useSkinIndex = 11; //穿戴的皮肤
  int32 dormIndex = 12; //坑位下标
  repeated PassengerTalent talents = 13; //天赋数据
  map<int32,int32> profile = 14; //资料数据
}

// 乘客天赋数据
message PassengerTalent{
  int32 id = 1; //天赋id
  int32 level = 2; //等级
}

// 乘客剧情数据
message PassengerPlot{
  string id = 1; //剧情id
  bool done = 2; //是否已完成
}

// 星球数据
message Planet{
  int32 id = 1; //星球id
  int32 curMapId = 2; //当前地图id
  int32 curNodeId = 3; //当前节点id
  double nodeProgress = 4; //当前节点进度
  int32 nodePathProgress = 5; //上个节点到当前节点进度
  bool landed = 6; //是否登陆过星球
  bool reached = 7; //是否到达过星球
  repeated PlanetBranch branches = 8; //星球支线
  map<int32,int32> profileData = 9; //资料数据
  repeated int32 profileCollectReward = 10; //领取的星球资料奖励区域数据
  int32 roleNum = 11; //宣传玩法人口数
  int32 publicityUnGetOutputTime = 12; //宣传玩法未收取时长累计
  int32 publicityOutputTime = 13; //宣传玩法已累计时间
}

// 探索数据
message PlanetInfo{
  int32 curPlanetId = 1; //当前星球id
  int32 moveTargetId = 2; //目标星球id
  int32 moveSurplusTime = 3; //航行结束剩余时间>=0
  repeated Planet planets = 4; //星球数据
  RageMode rageMode = 5; //狂暴模式
}

// 星球支线
message PlanetBranch{
  string id = 1; //支线id
  int32 mapId = 2; //地图id
  int32 nodeId = 3; //当前节点id
  repeated int32 nodeRewards = 4; //当前节点已领取奖励
}

// 狂暴模式
message RageMode{
  string id = 1; //狂暴模式节点id
  int32 count = 2; //狂暴模式次数
}

// 工具数据信息
message ToolInfo{
  int32 type = 1; //工具类型
  int32 lv = 2; //工具等级
}

// 任务目标数据
message TaskTarget{
  string id = 1; //当前目标id
  int32 num = 2; //当前目标数量
}

// 任务数据
message Task{
  string id = 1; //当前任务id
  repeated TaskTarget targets = 2; //任务目标进度
}

// 任务模块数据
message TaskInfo{
  repeated Task tasks = 1; //当前任务
  repeated string completes = 2; //已完成的任务Id
}

// 打造台数据
message ToolModel{
  int32 Lv = 1; //当前等级
  map<int32,ToolInfo> tools = 2; //工具集合
  int32 blessCount = 3; //祝福次数
  string blessId = 4; //祝福id
}

// 基本的角色数据信息
message Player{
  string uid = 1; //角色id
  string nickName = 2; //角色名称
  string avatarUrl = 3; //角色头像
  fixed64 createTime = 4; //创建时间
  fixed64 offlineTime = 5; //离线时间
  fixed64 totalOnlineTime = 6; //总在线时长
  int32 diamond = 7; //钻石
  int32 heart = 8; //爱心
  int32 starDust = 9; //星尘
  int32 paperCrane = 10; //纸鹤
  bool gm = 11; //是不是gm
  fixed64 time = 12; //游戏时长
  Energy energy = 13; //加速体力数据
  repeated ItemInfo bag = 14; //背包物品数据
  TrainInfo train = 15; //列车数据
  repeated PassengerInfo passengers = 16; //乘客数据
  PlanetInfo planetInfo = 17; //探索数据
  int32 guideId = 18; //当前引导模块id
  repeated GuideInfo guideInfo = 19; //引导数据
  TaskInfo taskInfo = 20; //任务数据
  ToolModel toolModel = 21; //打造台数据
  Explore explore = 22; //探索数据
  Wanted wanted = 23; //悬赏数据
  int32 nextDaySurpluTime = 24; //下次每日刷新剩余时间
  repeated MailInfo mailList = 25; //邮件列表(只有基本状态)
  AchievementInfo achievementInfo = 26; //成就任务数据
  int32 changeNameCnt = 27; //改名次数
  repeated NewMarkInfo newMarkList = 28; //new标签数据
  Chest chest = 29; //宝箱模块
  int32 heartOutput = 30; //爱心产出
  int32 passengerStarOutput = 31; //乘客小费产出
  Tower tower = 32; //爬塔模块
  BlackHole blackHole = 33; //黑洞模块
  Battle battle = 34; //战斗模块
  Resonance resonance = 35; //共鸣
  Equip equip = 36; //装备模块
  Instance instance = 37; //副本模块
  int32 passengerRestCdTime = 38; //乘客重置cd
  Store store = 39; //商店数据
  map<int32,PassengerSkinData> skin = 40; //皮肤数据
  Jackpot jackpot = 41; //抽卡模块数据
  Pay pay = 42; //支付模块数据
  Transport transport = 43; //护送模块数据
  map<string,string> configMd5 = 44; //服务器配置md5，仅仅dev下发
  Field field = 45; //农场模块数据
  map<int32,int32> frag = 46; //投影数据
  Ore ore = 47; //矿场模块数据
  Collect collect = 48; //收集玩法模块数据
  ArrestModule arrest = 49; //通缉令模块数据
  int32 nextWeekSurplusTime = 50; //下次周刷新时间
  SpaceStone spaceStone = 51; //空间石
  DailyTaskInfo dailyTask = 52; //每日任务
  repeated int32 passengerProfiles = 53; //乘客资料
  repeated int32 planetProfiles = 54; //星球资料
  int32 offlineRewardTime = 55; //离线奖励时长
  int32 offsetTime = 56; //偏移时间（debug环境使用）
  PvpModuleData pvpModuleData = 57; //pvp数据
  Ad ad = 58; //广告数据
  ProfileBranch profileBranch = 59; //记忆阁
  TrainDailyTask trainDailyTask = 60; //列车任务
  BurstTask burstTask = 61; //突发任务
  TrainActivity trainActivity = 62; //列车活动
  TechData techData = 63; //科技数据
  TimeStone timeStone = 64; //时间石
}

// 乘客对应皮肤信息组
message PassengerSkinData{
  repeated PassengerSkin list = 1; //皮肤列表
}

// 乘客对应皮肤信息
message PassengerSkin{
  int32 index = 1; //皮肤序号
}

// 货币信息
message CurrencyInfo{
  int32 type = 1; //货币类型 1钻石 2星尘 3爱心 4纸鹤
  int32 val = 2; //货币值
}

// 引导数据信息
message GuideInfo{
  int32 id = 1; //模块id
  repeated int32 keySteps = 2; //已经完成的关键步骤
}

// 邮件数据
message MailInfo{
  string id = 1; //id
  fixed64 time = 2; //创建时间
  string title = 3; //邮件标题
  string content = 4; //邮件内容
  repeated Condition rewards = 5; //附件
  bool read = 6; //是否读取
  bool attach = 7; //是否领取
}

// 怪物数据
message Monster{
  int32 id = 1; //id
  int32 lv = 2; //等级
}

// 悬赏模块数据
message Wanted{
  repeated WantedInfo list = 1; //悬赏列表
}

// 悬赏数据
message WantedInfo{
  int32 name = 1; //名字下标
  int32 level = 2; //等级
  int32 people = 3; //需求人数
  int32 surplusTime = 4; //剩余时间
  int32 state = 5; //状态
  repeated Condition rewards = 6; //奖励
  repeated WantedCondition conditions = 7; //条件
  repeated int32 roles = 8; //进行中的角色
  int32 publisher = 9; //发布者
  int32 type = 10; //类型
  int32 bg = 11; //主要事件
  int32 bgArg = 12; //事件参数
}

// 悬赏数据
message WantedCondition{
  int32 type = 1; //类型
  int32 value = 2; //值
}

// 成就任务数据
message AchievementInfo{
  repeated Task tasks = 1; //当前成就任务
  repeated string completes = 2; //已完成的成就任务Id
  repeated fixed64 completeTime = 3; //完成任务时间
}

// new标签
message NewMarkInfo{
  int32 typeId = 1; //类型
  repeated int32 aryVal = 2; //数据
}

// 仓库
message Storage{
  int32 Type = 1; //类型
  int32 Lv = 2; //等级
}

// 宝箱
message BoxInfo{
  int32 id = 1; //宝箱id
  int32 num = 2; //宝箱数量
}

// 宝箱数据集
message BoxInfoArray{
  repeated BoxInfo data = 1; //数据
}

// 宝箱模块
message Chest{
  int32 medal = 1; //总积分
  int32 step = 2; //当前档位
  map<int32,BoxInfoArray> data = 3; //宝箱集合
}

// 爬塔
message Tower{
  string checkPointId = 1; //当前关卡id
  bool isDone = 2; //已完成
}

// 黑洞
message BlackHole{
  string curId = 1; //当前节点id
  string nextId = 2; //下个节点id
  repeated BlackHoleNode map = 3; //地图
  repeated BlackHoleBuff buffs = 4; //buff
  repeated BattleRole roles = 5; //选择的角色
  repeated BattleRole aids = 6; //援助
  repeated string deads = 7; //已阵亡的角色
  BattleTeam team = 8; //编队
  int32 level = 9; //当前难度
  repeated BlackHoleBoss bosses = 10; //不同难度的boss队伍
  repeated BlackHoleEquip equips = 11; //科技装备
  int32 currency = 12; //星海币
  bool isUnlock = 13; //是否解锁
  double add = 14; //科技装备加成
}

// 黑洞节点
message BlackHoleNode{
  string id = 1; //节点id
  int32 type = 2; //节点类型
  repeated BattleRole enemies = 3; //敌人
  repeated BlackHoleBuff buffs = 4; //buff
  repeated BattleRole aids = 5; //援助
  bool isFog = 6; //是否为迷雾
  repeated Condition reward = 7; //奖励
  repeated BlackHoleEquip equips = 8; //科技装备
}

// 黑洞buff
message BlackHoleBuff{
  int32 index = 1; //buff下标
  int32 type = 2; //buff类型
  repeated string targets = 3; //作用uid
  map<string,int32> add = 4; //宝箱集合
}

// 黑洞科技装备
message BlackHoleEquip{
  int32 id = 1; //装备id
  int32 target = 2; //作用目标
  int32 level = 3; //等级
}

// 黑洞boss
message BlackHoleBoss{
  int32 level = 1; //难度
  repeated BattleRole roles = 2; //角色
}

// 战斗角色
message BattleRole{
  string uid = 1; //角色uid
  int32 id = 2; //角色id
  int32 type = 3; //乘客=1还是怪物=2
  int32 lv = 4; //角色等级
  int32 starLv = 5; //角色星级
  int32 attack = 6; //角色攻击
  int32 hp = 7; //角色血量
  repeated PassengerTalent talents = 8; //天赋数据
  repeated EquipItem equips = 9; //装备
  double attrRate = 10; //属性倍率
}

// 战斗角色
message BattleResult{
  bool isWin = 1; //我方是否胜利
  repeated string uids = 2; //存活角色uid
}

// 战斗编队
message BattleTeam{
  int32 id = 1; //编队id
  repeated string uids = 2; //角色
}

// 战斗编队
message Battle{
  repeated BattleTeam teams = 1; //队伍
}

// 装备信息
message Equip{
  repeated EquipItem data = 1; //装备列表
  map<string,int32> proficiency = 2; //打造台熟练度
}

// 装备词条
message EquipEffect{
  int32 id = 1; //id
  int32 attr = 2; //属性
  int32 level = 3; //等级
}

// 装备信息
message EquipItem{
  string uid = 1; //唯一id
  int32 id = 2; //配置id
  int32 level = 3; //等级
  repeated EquipEffect effects = 4; //词条
  bool used = 5; //是否穿戴中
}

// 每日副本信息
message Instance{
  int32 level = 1; //最后一个通关的关卡
  bool isUnlock = 2; //是否解锁
  bool isCompletePuzzle = 3; //是否完成解密
}

// 商店模块数据
message Store{
  repeated StoreInfo list = 1; //商店数据列表
}

// 商店数据
message StoreInfo{
  int32 id = 1; //id
  int32 refreshCount = 2; //已刷新次数
  repeated Goods goods = 3; //商品
  int32 refreshTime = 4; //下次刷新剩余时间
}

// 商品数据
message Goods{
  int32 stock = 1; //库存
  Condition item = 2; //道具
  int32 discount = 3; //折扣
  Condition cost = 4; //消耗的货币，num取打折后的值
}

// 抽卡模块数据
message Jackpot{
  int32 jackpotDailyNum = 1; //当日抽卡次数
  int32 jackpotTotalCount = 2; //总累计抽卡次数
  int32 jackpotPoints = 3; //抽卡积分
}

// 支付数据
message Pay{
  repeated NotFinishPayOrder notFinishOrders = 1; //未完成订单列表
  map<string,int32> payCountMap = 2; //部分商品的购买次数
}

// 未完成订单数据
message NotFinishPayOrder{
  string cpOrderId = 1; //订单id
  string productId = 2; //产品id
  int32 quantity = 3; //数量
  string platform = 4; //平台
}

// 餐厅菜单数据
message CarriageGoodsInfo{
  string id = 1; //id
  int32 lv = 2; //等级
}

// 共鸣数据
message Resonance{
  repeated ResonanceSlot slots = 1; //槽位
}

// 共鸣槽数据
message ResonanceSlot{
  int32 id = 1; //乘客id
  int32 cd = 2; //冷却时间
}

// 护送模块数据
message Transport{
  int32 exp = 1; //当前经验
  repeated TransportData list = 2; //
}

// 单个护送数据
message TransportData{
  int32 starLv = 1; //星级
  int32 start = 2; //起点星球
  int32 end = 3; //终点星球
  repeated Condition rewards = 4; //奖励数据
  repeated Condition fixRewards = 5; //额外必得奖励数据
  TransportDataState state = 6; //数据状态
  int32 load = 7; //任务负重要求
  bool rare = 8; //是不是稀有任务
  bool timeStoneKey = 9; //是不是时间之钥任务
  int32 actor = 10; //委托人
  string key = 11; //委托对话
  TransportBattleData battleData = 12; //海盗数据
}

// 护送的战斗数据
message TransportBattleData{
  repeated BattleRole monsters = 1; //敌人
  int32 second = 2; //第多少s触发战斗
}

// 农场数据
message Field{
  repeated FieldCeil ceilData = 1; //格子数据列表
  repeated Condition seedData = 2; //种子数据列表
  int32 Level = 3; //等级
  map<string,int32> levelCond = 4; //升级条件数据
}

// 矿场数据
message Ore{
  int32 recoverTime = 1; //下一个镐子回复时间
  map<int32,int32> oreItems = 2; //矿石数据
  repeated OreLevelData data = 3; //矿洞数据
  bool isUnlock = 4; //是否解锁
}

// 矿场难度对应的数据
message OreLevelData{
  int32 level = 1; //等级
  int32 depth = 2; //当前所在深度
  repeated OreRowData data = 3; //格子数据
  bool isSpecialArea = 4; //是否处于特殊区域层
}

// 矿场单行数据
message OreRowData{
  repeated OreCeilData data = 1; //行数据
}

// 矿场单格数据
message OreCeilData{
  OreCeilType type = 1; //格子类型
  repeated Condition oreExtra = 2; //如果是矿石格子,这个是矿石数据,怪物格子就是奖励数据
  repeated BattleRole monsters = 3; //如果是怪物格子,这个是战斗数据
  repeated Point ref = 4; //格子引用
  OrePageNextType nextType = 5; //下一层类型
  OreBlockItemDrillDirection direction = 6; //钻头方向
}

// 农场格子数据
message FieldCeil{
  int32 id = 1; //格子id
  int32 type = 2; //格子类型
  int32 surplusTime = 3; //生长剩余时间
  FieldCeilState state = 4; //状态
  int32 plantId = 5; //作物id
}

// 点位
message Point{
  int32 x = 1; //x
  int32 y = 2; //y
}

// 采集玩法
message Collect{
  repeated MapMineItemData mine = 1; //地图采集物数据
}

// 每日任务模块数据
message DailyTaskInfo{
  repeated DailyTask tasks = 1; //任务列表
  bool bigGet = 2; //是否领取大奖励
}

// 每日任务
message DailyTask{
  string uid = 1; //任务uid
  int32 id = 2; //任务id
  repeated Condition target = 3; //目标
  repeated Condition progress = 4; //进度
  repeated Condition reward = 5; //奖励
  int32 state = 6; //状态
  int32 sender = 7; //任务发起者
  int32 content = 8; //任务lang
  int32 planet = 9; //战斗所在星球
  repeated BattleRole battleInfo = 10; //战斗数据
}

// 地图采集物数据
message MapMineItemData{
  int32 id = 1; //采集物id
  int32 type = 2; //采集物类型
  repeated Condition reward = 3; //奖励
  Point position = 4; //位置
  string uid = 5; //唯一id
  double scale = 6; //缩放比例
  int32 planetId = 7; //星球id
}

// 时间宝石
message TimeStone{
  int32 lv = 1; //等级
  int32 energy = 2; //剩余能量
  repeated TimeStoneEventWrapper events = 3; //记录
}

// 抽卡记录包装
message TimeStoneEventWrapper{
  TimeStoneEvent type = 1; //类型
  google.protobuf.Any data = 2; //数据
}

// 抽卡记录-用于时间宝石
message JackpotEventData{
  int32 drawType = 1; //抽取类型
  int32 drawPid = 2; //乘客id
  bool isConvertIntoFragments = 3; //是否转换成碎片
  bool isDiamondDiscount = 4; //是否打折
  bool expired = 5; //是否过期
}

// 打造记录-用于时间宝石
message EquipMakeEventData{
  string uid = 1; //打造结果的装备id
  int32 equipId = 2; //打造结果的装备id
  string beanId = 3; //打造配置id
  bool expired = 4; //是否过期
}

// 迷宫战斗记录-用于时间宝石
message BlackHoleBattleEventData{
  int32 layer = 1; //
  int32 level = 2; //
  string curId = 3; //当前id
  string nextId = 4; //下个id
  repeated BattleRole roles = 5; //选择的角色
  repeated BlackHoleBuff buffs = 6; //buff
  repeated BattleRole aids = 7; //援助
  repeated BlackHoleEquip equips = 8; //科技装备
  repeated string deads = 9; //已阵亡的角色
  BattleTeam team = 10; //编队
  BlackHoleNode node = 11; //当前节点原始数据
  bool expired = 12; //是否过期
}

// 通缉令列表数据
message ArrestModule{
  repeated Arrest arrests = 1; //列表
  int32 score = 2; //声望
  ArrestResult result = 3; //上期战报
  int32 currency = 4; //货币
}

// 通缉令数据
message Arrest{
  string id = 1; //id
  int32 expirationTime = 2; //过期时间
  int32 planetId = 3; //星球id
  int32 star = 4; //星级
  ArrestState state = 5; //状态
  repeated BattleRole monsters = 6; //怪物数据
  repeated Condition rewards = 7; //奖励
  int32 storyId = 8; //罪名id
  repeated ArrestClue clues = 9; //线索
}

// 通缉令线索
message ArrestClue{
  ArrestClueType type = 1; //类型
  repeated int32 planets = 2; //星球id
  bool isHideInfo = 3; //是否隐藏信息
  ArrestPlaceType placeType = 4; //地点类型
  ArrestTimeType timeType = 5; //时间类型
}

// 通缉战报
message ArrestResult{
  repeated ArrestResultDetail wins = 1; //已完成
  repeated ArrestResultDetail fails = 2; //未完成
  int32 score = 3; //声望变化
}

// 通缉战报详情
message ArrestResultDetail{
  int32 star = 1; //星级
  int32 count = 2; //数量
}

// 空间宝石
message SpaceStone{
  repeated int32 marks = 1; //标记id
  int32 lv = 2; //等级
  int32 energy = 3; //剩余能量
}

// 玩家pvp模块数据
message PvpModuleData{
  map<int32,int32> ticket = 1; //挑战次数数据
  map<int32,int32> duration = 2; //持续时间数据
}

// 玩家普通pvp数据
message PvpNormalData{
  int32 score = 1; //积分
  int32 rank = 2; //排名
  repeated BattleRole battleRoles = 3; //阵容
}

// 玩家简单数据
message SimplePlayerData{
  string id = 1; //id
  string name = 2; //名字
  string head = 3; //头像
}

// 玩家简单pvp数据
message PvpSimplePlayerData{
  int32 score = 1; //积分
  int32 rank = 2; //排名
  SimplePlayerData ext = 3; //简要数据
  repeated BattleRole battleRoles = 4; //阵容
}

// pvp战绩数据
message PvpBattleRecordData{
  string docId = 1; //文档id
  PvpSimplePlayerData attacker = 2; //进攻方
  PvpSimplePlayerData defender = 3; //防守方
  int32 result = 4; //0平1胜2负
  int32 time = 5; //多久之前发生的战斗
  int32 scoreChange = 6; //积分变动
  int32 score1 = 7; //进攻方原始积分
  int32 score2 = 8; //防守方原始积分
}

// 探索队伍数据
message ExploreTeam{
  int32 planetId = 1; //星球ID
  repeated int32 roles = 2; //探索的乘客ID列表
  int32 surplusTime = 3; //剩余时间
  repeated Condition rewards = 4; //探索奖励
}

// 探索模块数据
message Explore{
  repeated ExploreTeam teams = 1; //探索队伍列表
}

// 广告模块数据
message Ad{
  map<int32,int32> data = 1; //广告数据
}

// 记忆阁
message ProfileBranch{
  int32 id = 1; //当前关卡id
  repeated ProfileBranchLevel levels = 2; //关卡列表
  int32 energy = 3; //体力
  int32 surplusTime = 4; //体力剩余时间
}

// 记忆阁关卡
message ProfileBranchLevel{
  int32 id = 1; //关卡id
  int32 nodeId = 2; //当前节点id
  repeated MapMineItemData nodes = 3; //节点列表
  bool unlock = 4; //是否解锁
}

// 列车任务
message TrainDailyTask{
  repeated TrainDailyTaskItem list = 1; //任务列表
}

// 列车任务
message TrainDailyTaskItem{
  int32 id = 1; //任务id
  int32 surplusTime = 2; //剩余时间
  CommonState state = 3; //状态
  repeated Condition rewards = 4; //奖励
  repeated WantedCondition conditions = 5; //条件
  repeated int32 roles = 6; //进行中的角色
  int32 people = 7; //限定数量
  int32 trainId = 8; //限定车厢
  int32 level = 9; //星级
}

// 突发任务
message BurstTask{
  repeated BurstTaskItem list = 1; //任务列表
}

// 突发任务
message BurstTaskItem{
  int32 id = 1; //任务id
  int32 surplusTime = 2; //剩余时间
  CommonState state = 3; //状态
  repeated Condition rewards = 4; //奖励
  repeated WantedCondition conditions = 5; //条件
  repeated int32 roles = 6; //进行中的角色
  int32 people = 7; //限定数量
  int32 trainId = 8; //限定车厢
}

// 列车活动
message TrainActivity{
  repeated TrainActivityItem list = 1; //任务列表
  int64 arrangeWorldTime = 2; //安排时的世界时间
  repeated TrainActivityUngetReward ungetRewards = 3; //未领取奖励
}

// 列车活动未领取奖励
message TrainActivityUngetReward{
  int32 trainId = 1; //车厢id
  repeated Condition rewards = 2; //奖励
}

// 列车活动数据
message TrainActivityItem{
  int32 id = 1; //id
  int32 cfgId = 2; //配置id
  int32 trainId = 3; //表现作用车厢id
  repeated Condition rewards = 4; //奖励
  int64 surplusTime = 5; //剩余时间
  int32 costDay = 6; //消耗天数
  CommonState state = 7; //状态
}

// 科技数据
message TechData{
  map<int32,int32> data = 1; //科技数据
}

//After are messages.
