<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <message type="C2S" name="CreatePayOrder" module="game" explain="创建订单">
        <field class="string" name="productId" explain="商品id"/>
        <field class="enum.OrderPayPlatform" name="orderPlatform" explain="支付平台"/>
    </message>
    <message type="S2C" name="CreatePayOrder" explain="创建订单">
        <field class="int32" name="code" explain="0"/>
        <field class="map" name="options" explain="数据选项">
            <key class="string" explain="key"/>
            <value class="string" explain="值"/>
        </field>
    </message>
    <message type="C2S" name="VerifyPayOrder" module="game" explain="验证订单">
        <field class="string" name="orderId" explain="订单id"/>
    </message>
    <message type="S2C" name="VerifyPayOrder" explain="验证订单">
        <field class="int32" name="code" explain="非0则请求失败"/>
        <field class="enum.OrderState" name="state" explain="订单状态"/>
    </message>
</messages>