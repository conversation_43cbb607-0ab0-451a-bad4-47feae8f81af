<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <enum name="OreCeilType" explain="矿洞格子类型">
        <type name="BlockNone" number="0" explain="空地" />
        <type name="BlockSingle" number="1" explain="敲一下地块" />
        <type name="BlockDouble" number="2" explain="敲两下地块" />
        <type name="BlockMonster" number="3" explain="怪物地块" />
        <type name="BlockSpecial" number="4" explain="特殊地块" />
        <type name="BlockNext" number="5" explain="下一区域" />
        <type name="BlockNextSpecial" number="6" explain="特殊区域" />
        <type name="BlockDoubleBreakage" number="7" explain="敲两下地块破损状态" />
        <type name="BlockBack" number="8" explain="从特殊区域返回" />
        <type name="BlockMonsterQunit" number="9" explain="精英怪" />
        <type name="BlockStartPs" number="10" explain="起点" />
        <type name="BlockItemBoom" number="11" explain="炸弹" />
        <type name="BlockItemDrill" number="12" explain="钻头" />
        <type name="BlockMonsterRunAway" number="13" explain="逃跑地精的战斗" />
    </enum>
    <enum name="OreCeilActionType" explain="矿洞格子操作类型">
        <type name="BreakNormal" number="0" explain="普通镐子" />
        <type name="BreakNormalPlus" number="1" explain="高级稿子" />
        <type name="BreakBomb" number="2" explain="炸弹" />
        <type name="BreakDrill" number="3" explain="钻头" />
        <type name="BreakBattle" number="4" explain="挑战" />
        <type name="GotoNext" number="5" explain="进入下一区域" />
        <type name="GotoSpecial" number="6" explain="特殊区域" />
    </enum>
    <enum name="OreCeilActionCode" explain="矿洞格子操作响应">
        <type name="OnSuccess" number="0" explain="成功" />
        <type name="InvalidPosition" number="1" explain="操作位置不正确" />
        <type name="InvalidActionType" number="2" explain="选中格子不允许进行这个操作" />
        <type name="ItemNotEnough" number="3" explain="道具不足" />
    </enum>
    <enum name="OreSpecialAreaType" explain="矿洞特殊区域类型">
        <type name="MonsterQunit" number="0" explain="精英怪" />
        <type name="OreArea" number="1" explain="矿洞" />
    </enum>
    <enum name="OrePageNextType" explain="日常挖矿特殊矿区">
        <type name="NormalNext" number="0" explain="正常矿区" />
        <type name="GrayNext" number="1" explain="(黑色)矿区" />
        <type name="BlueNext" number="2" explain="蓝色矿区" />
    </enum>
    <enum name="OreBlockItemDrillDirection" explain="钻头道具方向">
        <type name="Left" number="0" explain="左" />
        <type name="Top" number="1" explain="上" />
        <type name="Right" number="2" explain="右" />
        <type name="Bottom" number="3" explain="下" />
    </enum>
    <enum name="ArrestState" explain="通缉令状态">
        <type name="NotCollected" number="0" explain="未领取" />
        <type name="OnGoing" number="1" explain="进行中" />
        <type name="DoneNoReward" number="2" explain="完成未领奖" />
        <type name="Finished" number="3" explain="完成已领奖" />
        <type name="Expired" number="4" explain="过期" />
    </enum>
    <enum name="ArrestClueType" explain="通缉令线索类型">
        <type name="PLACE" number="0" explain="地点" />
        <type name="TIME" number="1" explain="时间" />
    </enum>
    <enum name="ArrestPlaceType" explain="通缉令地点类型">
        <type name="NONE" number="0" explain="空" />
        <type name="TEXT" number="1" explain="文案" />
        <type name="PIC" number="2" explain="图片" />
    </enum>
    <enum name="ArrestTimeType" explain="通缉令时间类型">
        <type name="ALL" number="0" explain="全天" />
        <type name="DAY" number="1" explain="白天" />
        <type name="NIGHT" number="2" explain="黑夜" />
    </enum>
    <enum name="PvpType" explain="竞技场类型">
        <type name="NORMAL" number="0" explain="普通竞技场" />
        <type name="HIGH" number="1" explain="高级竞技场" />
        <type name="PEAK" number="2" explain="巅峰竞技场" />
    </enum>
    <enum name="AdType" explain="广告类型">
        <type name="RecoveryTrainEnergy" number="0" explain="恢复列车加速引擎能量" />
        <type name="RecoveryOreBreakItem" number="1" explain="恢复矿洞镐子" />
    </enum>
    <enum name="ProfileBranchLevelState" explain="记忆阁关卡状态">
        <type name="Locked" number="0" explain="已锁定" />
        <type name="ReadyUnlock" number="1" explain="可以解锁" />
        <type name="Unlocked" number="2" explain="已解锁" />
        <type name="AnswerDone" number="3" explain="已完成" />
    </enum>
    <enum name="TransportDataState" explain="护送数据状态">
        <type name="NoneGet" number="0" explain="未领取" />
        <type name="Pull" number="1" explain="已经领取" />
        <type name="Done" number="2" explain="已经完成" />
        <type name="Over" number="3" explain="已经领奖" />
        <type name="Failed" number="4" explain="失败" />
    </enum>
    <enum name="FieldCeilState" explain="格子状态">
        <type name="Empty" number="0" explain="没有任何作物" />
        <type name="NotWater" number="1" explain="播种后未浇水" />
        <type name="Growing" number="2" explain="作物成长中" />
        <type name="GrowDone" number="3" explain="可以收获" />
    </enum>
    <enum name="CommonState" explain="车厢日常任务状态">
        <type name="NotStart" number="0" explain="未开始" />
        <type name="InProcess" number="1" explain="进行中" />
        <type name="DoneWithoutReward" number="2" explain="结束未领奖" />
        <type name="FinishWithReward" number="3" explain="结束已领奖" />
    </enum>
    <enum name="TrainTechEffectType" explain="车厢科技效果类型">
        <type name="ErrorEffect" number="0" explain="无效果" />
        <type name="AddDeepExploreShipNum" number="1" explain="增加深度探索飞船数量" />
        <type name="AddBedNum" number="2" explain="对应寝室床位增加" />
        <type name="AddTrainSpeed" number="3" explain="列车行驶速度增加,减少航行时间" />
        <type name="AddTrainDailyTaskNum" number="4" explain="列车任务数量增加" />
        <type name="AddWorkPlaceNum" number="5" explain="对应车厢工位增加" />
        <type name="AddDeepExploreSpeed" number="6" explain="深度探索速度增加" />
        <type name="AddTimeMachineSpeed" number="7" explain="时光机加速效果提升" />
        <type name="AddTrainLoad" number="8" explain="车厢载重提升" />
        <type name="AddHeartOutputPerHour" number="101" explain="每小时爱心产出增加" />
        <type name="AddHeartOutputTotal" number="102" explain="爱心产出总量增加" />
        <type name="AddStardustOutputPerHour" number="103" explain="每小时星尘产出增加" />
        <type name="AddStardustOutputTotal" number="104" explain="星尘产出总量增加" />
        <type name="AddElectricityOutputPerHour" number="105" explain="每小时电力产出增加" />
        <type name="AddElectricityOutputTotal" number="106" explain="电力产出总量增加" />
        <type name="AddVitalityOutputPerHour" number="107" explain="每小时元气值产出增加" />
        <type name="AddVitalityOutputTotal" number="108" explain="元气值产出总量增加" />
        <type name="AddWaterOutputPerHour" number="109" explain="每小时水资源产出增加" />
        <type name="AddWaterOutputTotal" number="110" explain="水资源产出总量增加" />
    </enum>
    <enum name="TimeStoneEvent" explain="时间宝石事件类型">
        <type name="TypeNone" number="0" explain="未知" />
        <type name="TypeBlackHoleBattle" number="1" explain="星海迷宫战斗" />
        <type name="TypeEquipMake" number="2" explain="装备打造" />
        <type name="TypeJackpot" number="3" explain="乘客邀请" />
    </enum>
    <enum name="OrderState" explain="订单状态">
        <type name="NOT_PAY" number="0" explain="未支付" />
        <type name="PAY" number="1" explain="已支付未发放" />
        <type name="FINISH" number="2" explain="已发放" />
        <type name="REFUND" number="3" explain="已退款" />
        <type name="FAIL_TIMEOUT" number="4" explain="失败-支付等待超时" />
    </enum>
    <enum name="OrderPayPlatform" explain="订单支付平台">
        <type name="PayNone" number="0" explain="未知" />
        <type name="WechatPay" number="1" explain="微信" />
        <type name="AliPay" number="2" explain="支付宝" />
        <type name="Apple" number="3" explain="苹果" />
        <type name="Google" number="4" explain="谷歌" />
    </enum>
</messages>