package main

import (
	"fmt"
	"log"
	"materials/obj"
	"os"
	"sort"
	ut "train/utils"

	"github.com/samber/lo"
)

// 车厢消耗数据 key是星球进度
var costMap = make(map[int][]*obj.Condition)

// 星球奖励数据 key是星球进度
var rewardsMap = make(map[int][]*obj.Condition)

// 分配数据 外层key是星球进度 内层key是材料id 内层value是材料数量
var allocationMap = make(map[int]map[int]int)

func Calculate() {
	calculateCostMap()
	v := costMap[104]
	fmt.Print(v)
	// 按进度顺序处理消耗数据
	// calculateRewardsMap()
	allocation()
	// 再次进行性价比分配
	allocationByCp()
	x := allocationMap[104]
	fmt.Print(x)
	writeAlloc()
}

// 分配
func allocation() {
	// 先分配fixed.txt中的材料
	for i, v := range fixedAllocation {
		plant := obj.ChapterPlanetMineAry[i]
		if plant.Reward == nil {
			plant.Reward = make([]*obj.Condition, 0)
		}
		for id, num := range v {
			plant.Reward = append(plant.Reward, &obj.Condition{Type: 11, Id: 101 + id, Num: num})
		}
		plant.ByFixed = true
	}

	// 按进度顺序
	keys := lo.Keys(costMap)
	sort.Slice(keys, func(i, j int) bool { return keys[i] < keys[j] })

	for _, line := range keys {
		alloc := allocationMap[line]
		if alloc == nil {
			alloc = make(map[int]int)
			allocationMap[line] = alloc
		}

		// 当前进度下能通关的采集物
		plants := getCurLinePlanets(line)
		// 当前进度下材料消耗
		costs := lo.Filter(costMap[line], func(item *obj.Condition, _ int) bool { return item.Type == 11 })
		// 先尝试将材料消耗，分配到采集物
		for _, cost := range costs {
			// 根据消耗类型，筛选出对应类型的采集物
			var canOperationPlants []*obj.ChapterPlanetMine
			switch true {
			case cost.IsWood():
				canOperationPlants = lo.Filter(plants, func(item *obj.ChapterPlanetMine, _ int) bool { return !item.ByFixed && item.IsWood() })
			case cost.IsStone():
				canOperationPlants = lo.Filter(plants, func(item *obj.ChapterPlanetMine, _ int) bool { return !item.ByFixed && item.IsStone() })
			case cost.IsPart():
				canOperationPlants = lo.Filter(plants, func(item *obj.ChapterPlanetMine, _ int) bool { return !item.ByFixed && item.IsPart() })
			}
			if len(canOperationPlants) == 0 {
				continue
			}
			// 特殊处理ChapterPlanetSp奖励数据 减去对应的材料
			// if line > 12 {
			// 	for _, sp := range obj.ChapterPlanetMineSpAry {
			// 		sp.Reward = obj.MergeConditions(sp.Reward...)
			// 		for _, reward := range sp.Reward {
			// 			if reward.Id == cost.Id {
			// 				cost.Num -= reward.Num
			// 			}
			// 		}
			// 	}
			// }
			// cost 要减去之前已经分配的数量 这里不用在乎顺序
			for _, vCost := range allocationMap {
				for kId, vNum := range vCost {
					if kId == cost.Id {
						cost.Num -= vNum
					}
				}
			}
			// 还要减去固定分配
			for _, v := range fixedAllocation {
				n := v[cost.Id-101]
				if n > 0 {
					cost.Num -= n
				}
			}
			if cost.Num <= 0 {
				continue
			}

			total := cost.Num
			plantsLen := len(canOperationPlants)
			// 策略是倒叙分配，优先分配给最后一个节点并且其数量一定大于0
			logic := func() {
				_continue := false
				for total > 0 {
					// 倒叙优先，平均分配
					for i := plantsLen - 1; i >= 0; i-- {
						plant := canOperationPlants[i]
						if plant.Reward == nil {
							plant.Reward = make([]*obj.Condition, 0)
						}
						num := 1
						if !_continue {
							// 如果当前节点已经有相同的材料类型了，那就不分配并且结束循环，因为它之前的节点一定已经被占用了
							ary := lo.Filter(plant.Reward, func(item *obj.Condition, _ int) bool { return item.Id == cost.Id })
							if len(ary) > 0 {
								if i != plantsLen-1 {
									break
								}
								// 只有最后一个节点并且已经被用了，那就全分配
								num = total
								total = 0
							}
						}

						plant.Reward = append(plant.Reward, &obj.Condition{Type: cost.Type, Id: cost.Id, Num: num})
						plant.Reward = obj.MergeConditions(plant.Reward...)
						total = ut.Max(0, total-num)
						if total == 0 {
							break
						}
					}
					_continue = true
				}
			}
			// 保证如果还没分配完 再进行一次分配
			for total > 0 {
				logic()
			}
			// 记录已经分配的数量
			alloc[cost.Id] += cost.Num
		}
	}

}

func allocationByCp() {
	// 分类，分别计算
	// plants := lo.Filter(obj.ChapterPlanetMineAry, func(item *obj.ChapterPlanetMine, _ int) bool { return !item.ByFixed })

	// for i := 0; i < len(plants); i++ {
	// 	cur := plants[i]
	// 	next := plants[i+1]
	// 	if len(cur.Reward) == 0 {
	// 		fmt.Println(next.Belong)
	// 	}
	// }

	// pops := make([]*obj.ChapterPlanetMine, 0)
	// for _, item := range plants {
	// 	pops = append(pops, item)
	// 	// 计算平均性价比
	// 	popsLen := len(pops)
	// 	if popsLen > 1 {
	// 		totalCp := 0.0
	// 		lo.ForEach(pops, func(pop *obj.ChapterPlanetMine, _ int) { totalCp += pop.CP() })
	// 		popsAvgCP := totalCp / float64(popsLen)
	// 		// 性价比差值不超过5
	// 		if math.Abs(item.CP()-popsAvgCP) <= 5 {
	// 			continue
	// 		}
	// 		// 超过5就看看能不能重新分配
	// 		for _, reward := range item.Reward {
	// 			var last *obj.ChapterPlanetMine
	// 			ary := lo.Filter(pops, func(pop *obj.ChapterPlanetMine, _ int) bool {
	// 				if item.IsPart() && !pop.IsPart() {
	// 					return false
	// 				}
	// 				if item.IsStone() && !pop.IsStone() {
	// 					return false
	// 				}
	// 				if item.IsWood() && !pop.IsWood() {
	// 					return false
	// 				}
	// 				if pop == item {
	// 					return false
	// 				}
	// 				// 和自己相同进度
	// 				if pop.Belong == item.Belong {
	// 					return true
	// 				}
	// 				// 上一个进度的最后一个节点
	// 				if pop.Belong > item.Belong && last != pop {
	// 					last = pop
	// 					return false
	// 				}
	// 				// 奖励符合或者没有奖励的
	// 				if pop.Reward == nil {
	// 					return true
	// 				}
	// 				_, exists := lo.Find(pop.Reward, func(r *obj.Condition) bool { return r.Id == reward.Id })
	// 				return exists
	// 			})
	// 			if len(ary) == 0 {
	// 				continue
	// 			}

	// 			fmt.Println(ary)
	// 		}
	// 	}

	// }

}

func writeAlloc() {
	dot := make([][]int, 0)
	for i := 0; i < len(obj.ChapterPlanetMineAry); i++ {
		plant := obj.ChapterPlanetMineAry[i]
		cur := make([]int, 24)
		for _, reward := range plant.Reward {
			if plant.MineId != 1024 {
				cur[reward.Id-101] += reward.Num
			} else {
				cur[reward.Id-101] = reward.Num
			}
		}
		dot = append(dot, cur)
	}

	// 将结果写入result.txt
	file, err := os.Create("result.txt")
	if err != nil {
		fmt.Printf("写入result.txt失败: %v\n", err)
		return
	}
	defer file.Close()
	for _, d := range dot {
		for _, v := range d {
			fmt.Fprintf(file, "%d\t", v)
		}
		fmt.Fprintln(file)
	}
	fmt.Println("分配结果已写入result.txt")
}

// getCurLinePlanets 根据星球进度行号  获取当前进度下通关的采集物节点切片
//
// Parameters:
//   - line int
//
// Returns:
//   - []*obj.ChapterPlanetMine
func getCurLinePlanets(line int) []*obj.ChapterPlanetMine {
	tmp := obj.PlanetNodeAry[:line-2]
	ary := lo.Filter(tmp, func(item *obj.PlanetNode, _ int) bool { return item.Type == 0 })

	return lo.Map(ary, func(node *obj.PlanetNode, _ int) *obj.ChapterPlanetMine {
		id := fmt.Sprintf("%d-%d-%d", node.PlanetId, node.MapId, node.TypeIndex)
		it, exists := lo.Find(obj.ChapterPlanetMineAry, func(item *obj.ChapterPlanetMine) bool {
			return item.Id == id
		})
		if !exists {
			log.Fatalf("ChapterPlanetMine找不到对应的节点? %s", id)
			return nil
		}
		it.Belong = line
		return it
	})
}

func calculateCostMap() {
	// 通过星球进度和车厢进度，计算当前进度下需要消耗的材料
	// 还有修建车厢的消耗
	for _, v := range obj.ProgressMap {
		totalCost := make([]*obj.Condition, 0)
		for trainId, progress := range v.Data {
			if progress <= 0 {
				continue
			}
			start := trainProgressAdd[trainId]
			its := obj.TrainItemLevelAry[start : start+progress]
			lo.ForEach(its, func(it *obj.TrainItemLevel, _ int) {
				totalCost = append(totalCost, it.BuyCost...)
			})
		}

		for _, trainId := range v.Events {
			if trainId == 0 {
				continue
			}
			train, exists := lo.Find(obj.TrainAry, func(item *obj.Train) bool { return item.Id == trainId })
			if !exists {
				log.Fatalf("Train找不到对应的车厢配置? %d", trainId)
				continue
			}
			totalCost = append(totalCost, train.BuyCost...)
		}
		costMap[v.Line] = obj.MergeConditions(totalCost...)
	}
}

// func calculateRewardsMap() {
// 	// 通过星球进度，计算每个星球进度能够获得的材料
// 	for _, v := range obj.ProgressMap {
// 		totalRewards := make([]*obj.Condition, 0)
// 		tmp := obj.PlanetNodeAry[:v.Line]
// 		for _, node := range tmp {
// 			if node.Type == 0 {
// 				id := fmt.Sprintf("%d-%d-%d", node.PlanetId, node.MapId, node.TypeIndex)
// 				it, exists := lo.Find(obj.ChapterPlanetMineAry, func(item *obj.ChapterPlanetMine) bool {
// 					return item.Id == id
// 				})
// 				if !exists {
// 					log.Fatalf("ChapterPlanetMine找不到对应的节点? %s", id)
// 					continue
// 				}
// 				totalRewards = append(totalRewards, it.Reward...)
// 				totalRewards = obj.MergeConditions(totalRewards...)
// 			}
// 		}
// 		rewardsMap[v.Line] = totalRewards
// 	}
// }
