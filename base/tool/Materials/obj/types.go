package obj

import (
	"fmt"
	"log"
	"strings"

	"github.com/samber/lo"
	"github.com/spf13/cast"
)

var WoodItemId = []int{101, 104, 107, 110, 113, 116, 119, 122}
var StoneItemId = []int{102, 105, 108, 111, 114, 117, 120, 123}
var PartItemId = []int{103, 106, 109, 112, 115, 118, 121, 124}

var ProgressMap []*Progress // 星球进度切片

type Progress struct {
	Line   int         // 星球进度
	Data   map[int]int // 车厢id - 进度
	Events []int       // 事件进度
}
type TrainProgress struct {
	Lines []string
}

type Condition struct {
	Type int `json:"type"`
	Id   int `json:"id"`
	Num  int `json:"num"`
}

// 木材
func (c *Condition) IsWood() bool {
	return c.Type == 11 && lo.Contains(WoodItemId, c.Id)
}

// 石头
func (c *Condition) IsStone() bool {
	return c.Type == 11 && lo.Contains(StoneItemId, c.Id)
}

// 零件
func (c *Condition) IsPart() bool {
	return c.Type == 11 && lo.Contains(PartItemId, c.Id)
}

var PlanetNodeAry []*PlanetNode

type PlanetNode struct {
	Id        string `json:"id"`
	Type      int    `json:"type"`
	PlanetId  int    `json:"planetId"`
	MapId     int    `json:"mapId"`
	TypeIndex int    `json:"typeIndex"`
}

var ChapterPlanetMineAry []*ChapterPlanetMine

type ChapterPlanetMine struct {
	Id      string       `json:"id"`
	MineId  int          `json:"mineId"`
	Hp      int          `json:"hp"`
	Lv      int          `json:"lv"`
	Restore int          `json:"restore"` // 自愈 - 植物
	Defense int          `json:"defense"` // 防御 - 矿物
	Dodge   int          `json:"dodge"`   // 闪避 - 零件
	Reward  []*Condition `json:"-"`       // 这个奖励由程序计算
	ByFixed bool         `json:"-"`       // 是否由fixed.txt分配奖励
	Belong  int          `json:"-"`       // 属于星球进度第几行
}

// 植物
func (c *ChapterPlanetMine) IsWood() bool {
	return strings.HasPrefix(cast.ToString(c.MineId), "1") || c.Restore > 0
}

// 矿物
func (c *ChapterPlanetMine) IsStone() bool {
	// 1024是学院星 胡桃夹子那个特殊节点
	return (strings.HasPrefix(cast.ToString(c.MineId), "2") || c.Defense > 0) && c.MineId != 1024
}

// 零件
func (c *ChapterPlanetMine) IsPart() bool {
	return strings.HasPrefix(cast.ToString(c.MineId), "3") || c.Dodge > 0
}

// 性价比
func (c *ChapterPlanetMine) CP() float64 {
	cp := 0
	// 先累加奖励值
	for _, v := range c.Reward {
		if v.Type != 11 {
			continue
		}
		cp += ((v.Id-101)/3 + 1) * v.Num
	}
	// 计算该植物被砍倒需要的点击次数，工具等级按最大计算
	level := c.Lv
	typ := 0
	switch true {
	case c.IsWood():
		typ = 1
	case c.IsStone():
		typ = 2
	case c.IsPart():
		typ = 3
	}
	// 获取工具
	tool, _ := lo.Find(ToolLevelAry, func(item *ToolLevel) bool {
		return item.Type == typ && item.Level >= level
	})
	if tool == nil {
		log.Fatalln("应该不会找不到工具配置吧！？？？")
	}
	// 计算需要的点击次数
	clickTimes := c.Hp / tool.Attack
	// 性价比
	return float64(cp) / float64(clickTimes) * 10
}

// 特殊节点
var ChapterPlanetMineSpAry []*ChapterPlanetMineSp

type ChapterPlanetMineSp struct {
	Reward []*Condition `json:"reward"`
}

var ToolLevelAry []*ToolLevel

type ToolLevel struct {
	Id     string `json:"id"`
	Attack int    `json:"attack"`
	Amp    int    `json:"amp"`
	Hit    int    `json:"hit"`
	Level  int    `json:"-"`
	Type   int    `json:"-"`
}

var TrainAry []*Train

type Train struct {
	Id      int          `json:"id"`
	BuyCost []*Condition `json:"buyCost"`
}

var TrainItemLevelAry []*TrainItemLevel

type TrainItemLevel struct {
	Id      string       `json:"id"`
	BuyCost []*Condition `json:"buyCost"`
}

func MergeConditions(conds ...*Condition) []*Condition {
	data := make(map[string]*Condition)

	for _, cond := range conds {
		if cond.Type != 11 {
			continue
		}
		id := fmt.Sprintf("%d-%d", cond.Type, cond.Id)
		v, ok := data[id]
		if !ok {
			v = &Condition{Type: cond.Type, Id: cond.Id, Num: 0}
		}
		v.Num += cond.Num
		data[id] = v
	}
	return lo.Values(data)
}
